import React, { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import type { PlaybackTimeRange } from '../components/PlaybackControl';
import type { ReadableTopic } from '../components/TopicReader';

interface PlaybackContextType {
  // Time state
  currentTime: number;
  timeRange: PlaybackTimeRange | null;

  // Playback state
  isPlaying: boolean;
  playbackSpeed: number;

  // Topic state
  selectedTopic: ReadableTopic | null;

  // Actions
  setCurrentTime: (time: number) => void;
  setIsPlaying: (playing: boolean) => void;
  setPlaybackSpeed: (speed: number) => void;
  setSelectedTopic: (topic: ReadableTopic | null) => void;

  // Control methods
  play: () => void;
  pause: () => void;
  togglePlayPause: () => void;
  seekTo: (time: number) => void;
}

const PlaybackContext = createContext<PlaybackContextType | undefined>(undefined);

export const usePlayback = () => {
  const context = useContext(PlaybackContext);
  if (context === undefined) {
    throw new Error('usePlayback must be used within a PlaybackProvider');
  }
  return context;
};

interface PlaybackProviderProps {
  children: ReactNode;
}

export const PlaybackProvider: React.FC<PlaybackProviderProps> = ({ children }) => {
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [timeRange, setTimeRange] = useState<PlaybackTimeRange | null>(null);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [playbackSpeed, setPlaybackSpeed] = useState<number>(1);
  const [selectedTopic, setSelectedTopic] = useState<ReadableTopic | null>(null);

  // Fetch time range on mount
  useEffect(() => {
    const fetchTimeRange = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/playback/timerange');
        if (response.ok) {
          const range = await response.json();
          console.log('Time range loaded:', range);
          setTimeRange(range);
          setCurrentTime(range.start); // Start at the beginning
        } else {
          console.error('Failed to fetch time range');
        }
      } catch (error) {
        console.error('Error fetching time range:', error);
      }
    };

    fetchTimeRange();
  }, []);

  // Playback timer effect
  useEffect(() => {
    if (!isPlaying || !timeRange) {
      return;
    }

    const interval = setInterval(() => {
      setCurrentTime(prevTime => {
        // Advance by a reasonable amount based on the total duration
        // For a ~141 second duration, advance by ~1000ms per update for 1x speed
        const timeStep = (timeRange.duration / 141) * playbackSpeed; // ~1000ms per step at 1x speed
        const nextTime = prevTime + timeStep;

        // Stop at the end
        if (nextTime >= timeRange.end) {
          setIsPlaying(false);
          return timeRange.end;
        }

        return nextTime;
      });
    }, 100); // Update every 100ms for smooth playback

    return () => clearInterval(interval);
  }, [isPlaying, playbackSpeed, timeRange]);

  // Control methods
  const play = useCallback(() => {
    if (timeRange && currentTime >= timeRange.end) {
      // If at the end, restart from beginning
      setCurrentTime(timeRange.start);
    }
    setIsPlaying(true);
  }, [currentTime, timeRange]);

  const pause = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const togglePlayPause = useCallback(() => {
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  }, [isPlaying, play, pause]);

  const seekTo = useCallback((time: number) => {
    if (timeRange) {
      const clampedTime = Math.max(timeRange.start, Math.min(timeRange.end, time));
      setCurrentTime(clampedTime);
    }
  }, [timeRange]);

  // Handle playback speed change
  const handleSpeedChange = useCallback((speed: number) => {
    setPlaybackSpeed(speed);
  }, []);

  const contextValue: PlaybackContextType = {
    // State
    currentTime,
    timeRange,
    isPlaying,
    playbackSpeed,
    selectedTopic,

    // Setters
    setCurrentTime,
    setIsPlaying,
    setPlaybackSpeed: handleSpeedChange,
    setSelectedTopic,

    // Control methods
    play,
    pause,
    togglePlayPause,
    seekTo
  };

  return (
    <PlaybackContext.Provider value={contextValue}>
      {children}
    </PlaybackContext.Provider>
  );
};

export default PlaybackContext;
