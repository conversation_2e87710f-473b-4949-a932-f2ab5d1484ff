/**
 * Interface for ROS log message from /rosout_agg topic
 */
export interface LogMessage {
  timestamp: number;
  level: number; // 1=INFO, 2=WARN, 3=ERROR, 4=FATAL
  name: string; // Node name
  msg: string; // Log message text
  file: string; // Source file (optional)
  function: string; // Function name (optional)
  line: number; // Line number (optional)
}

/**
 * Log level constants
 */
export const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  FATAL: 4
} as const;

/**
 * Log level type
 */
export type LogLevelType = typeof LogLevel[keyof typeof LogLevel];

/**
 * Helper function to get log level name
 */
export function getLogLevelName(level: number): string {
  switch (level) {
    case LogLevel.DEBUG: return 'DEBUG';
    case LogLevel.INFO: return 'INFO';
    case LogLevel.WARN: return 'WARN';
    case LogLevel.ERROR: return 'ERROR';
    case LogLevel.FATAL: return 'FATAL';
    default: return 'UNKNOWN';
  }
}

/**
 * Helper function to get log level color class
 */
export function getLogLevelColor(level: number): string {
  switch (level) {
    case LogLevel.DEBUG: return 'text-gray-400';
    case LogLevel.INFO: return 'text-blue-400';
    case LogLevel.WARN: return 'text-yellow-400';
    case LogLevel.ERROR: return 'text-red-400';
    case LogLevel.FATAL: return 'text-red-600';
    default: return 'text-gray-400';
  }
}

/**
 * Helper function to get log level background color class
 */
export function getLogLevelBgColor(level: number): string {
  switch (level) {
    case LogLevel.DEBUG: return 'bg-gray-100';
    case LogLevel.INFO: return 'bg-blue-100';
    case LogLevel.WARN: return 'bg-yellow-100';
    case LogLevel.ERROR: return 'bg-red-100';
    case LogLevel.FATAL: return 'bg-red-200';
    default: return 'bg-gray-100';
  }
}
