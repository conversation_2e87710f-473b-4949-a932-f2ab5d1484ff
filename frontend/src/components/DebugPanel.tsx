import React from 'react';
import { usePlayback } from '../contexts/PlaybackContext';

/**
 * Debug panel to show current state and help troubleshoot issues
 */
const DebugPanel: React.FC = () => {
  const {
    currentTime,
    timeRange,
    isPlaying,
    playbackSpeed,
    selectedTopic
  } = usePlayback();

  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toISOString();
  };

  const getRelativeTime = (): string => {
    if (!timeRange) return '0s';
    const relativeTime = (currentTime - timeRange.start) / 1000;
    return `${relativeTime.toFixed(1)}s`;
  };

  const getProgress = (): string => {
    if (!timeRange) return '0%';
    const progress = ((currentTime - timeRange.start) / timeRange.duration) * 100;
    return `${progress.toFixed(1)}%`;
  };

  return (
    <div className="bg-gray-100 p-4 rounded-lg border-2 border-dashed border-gray-300">
      <h3 className="text-lg font-semibold text-gray-800 mb-3">🐛 Debug Panel</h3>
      
      <div className="grid grid-cols-2 gap-4 text-sm">
        <div>
          <h4 className="font-medium text-gray-700 mb-2">Playback State</h4>
          <div className="space-y-1">
            <div>
              <span className="text-gray-600">Playing:</span>
              <span className={`ml-2 font-mono ${isPlaying ? 'text-green-600' : 'text-red-600'}`}>
                {isPlaying ? '▶️ TRUE' : '⏸️ FALSE'}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Speed:</span>
              <span className="ml-2 font-mono text-blue-600">{playbackSpeed}x</span>
            </div>
            <div>
              <span className="text-gray-600">Progress:</span>
              <span className="ml-2 font-mono text-purple-600">{getProgress()}</span>
            </div>
          </div>
        </div>

        <div>
          <h4 className="font-medium text-gray-700 mb-2">Time Info</h4>
          <div className="space-y-1">
            <div>
              <span className="text-gray-600">Current:</span>
              <span className="ml-2 font-mono text-blue-600 text-xs">{currentTime.toFixed(0)}</span>
            </div>
            <div>
              <span className="text-gray-600">Relative:</span>
              <span className="ml-2 font-mono text-green-600">{getRelativeTime()}</span>
            </div>
            {timeRange && (
              <>
                <div>
                  <span className="text-gray-600">Start:</span>
                  <span className="ml-2 font-mono text-gray-500 text-xs">{timeRange.start.toFixed(0)}</span>
                </div>
                <div>
                  <span className="text-gray-600">End:</span>
                  <span className="ml-2 font-mono text-gray-500 text-xs">{timeRange.end.toFixed(0)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Duration:</span>
                  <span className="ml-2 font-mono text-orange-600">{(timeRange.duration / 1000).toFixed(1)}s</span>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="col-span-2">
          <h4 className="font-medium text-gray-700 mb-2">Selected Topic</h4>
          {selectedTopic ? (
            <div className="bg-white p-2 rounded border">
              <div className="text-xs space-y-1">
                <div>
                  <span className="text-gray-600">Name:</span>
                  <span className="ml-2 font-mono text-blue-600">{selectedTopic.name}</span>
                </div>
                <div>
                  <span className="text-gray-600">Type:</span>
                  <span className="ml-2 font-mono text-green-600">{selectedTopic.type}</span>
                </div>
                <div>
                  <span className="text-gray-600">Messages:</span>
                  <span className="ml-2 font-mono text-purple-600">{selectedTopic.messageCount.toLocaleString()}</span>
                </div>
                <div>
                  <span className="text-gray-600">Frequency:</span>
                  <span className="ml-2 font-mono text-orange-600">{selectedTopic.frequency.toFixed(2)} Hz</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 italic">No topic selected</div>
          )}
        </div>
      </div>

      <div className="mt-3 pt-3 border-t border-gray-300">
        <div className="text-xs text-gray-500">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;
