import React, { useState, useEffect, useCallback } from 'react';

interface Topic {
  name: string;
  type: string;
  frequency: number;
  messageCount: number;
  firstTimestamp: number;
  lastTimestamp: number;
}

interface Message {
  timestamp: number;
  data: any;
  sequenceNumber?: number;
}

interface TopicReaderProps {
  currentTime: number;
  isPlaying: boolean;
  onTopicSelect: (topic: Topic | null) => void;
}

const TopicReader: React.FC<TopicReaderProps> = ({
  currentTime,
  isPlaying,
  onTopicSelect
}) => {
  const [topics, setTopics] = useState<Topic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [currentMessage, setCurrentMessage] = useState<Message | null>(null);
  const [loading, setLoading] = useState(true);
  const [messageLoading, setMessageLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch topics on component mount
  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:3001/api/topics/readable');
        if (response.ok) {
          const topicsData = await response.json();
          setTopics(topicsData);
        } else {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      } catch (err) {
        console.error('Error fetching topics:', err);
        setError('Failed to load topics. Make sure the backend server is running.');
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, []);

  // Fetch message at current time when time changes or topic changes
  useEffect(() => {
    if (!selectedTopic || currentTime === 0) {
      setCurrentMessage(null);
      return;
    }

    const fetchMessage = async () => {
      try {
        setMessageLoading(true);
        const encodedTopicName = encodeURIComponent(selectedTopic.name);
        const url = `http://localhost:3001/api/topics/${encodedTopicName}/message?timestamp=${currentTime}`;
        const response = await fetch(url);

        if (response.ok) {
          const message = await response.json();
          setCurrentMessage(message);
        } else if (response.status === 404) {
          setCurrentMessage(null);
        } else {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      } catch (err) {
        console.error('Error fetching message:', err);
        setCurrentMessage(null);
      } finally {
        setMessageLoading(false);
      }
    };

    // For real-time streaming during playback, fetch immediately
    if (isPlaying) {
      fetchMessage();
    } else {
      const timeoutId = setTimeout(fetchMessage, 50);
      return () => clearTimeout(timeoutId);
    }
  }, [selectedTopic, currentTime, isPlaying]);

  // Handle topic selection
  const handleTopicSelect = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const topicName = event.target.value;

    // Prevent selecting empty option when playing
    if (!topicName && isPlaying) {
      // Reset the select to the current topic
      event.target.value = selectedTopic?.name || '';
      return;
    }

    if (topicName) {
      const topic = topics.find(t => t.name === topicName) || null;
      setSelectedTopic(topic);
      onTopicSelect(topic);
    } else {
      setSelectedTopic(null);
      onTopicSelect(null);
    }
  }, [topics, onTopicSelect, isPlaying, selectedTopic]);

  // Format message data for display
  const formatMessageData = useCallback((data: any): string => {
    try {
      return JSON.stringify(data, null, 2);
    } catch {
      return String(data);
    }
  }, []);

  // Format timestamp
  const formatTimestamp = useCallback((timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toISOString();
  }, []);

  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-md">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-12 bg-gray-200 rounded"></div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 p-6 rounded-lg border border-red-200">
        <div className="text-center">
          <h3 className="text-lg font-bold text-red-800 mb-2">Error Loading Topics</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg border border-gray-200">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-gray-800">📡 Topic Reader</h3>
          <div className="text-sm text-gray-500 bg-blue-100 px-3 py-1 rounded-full">
            {topics.length} topics available
          </div>
        </div>

        {/* Topic Selection */}
        <div className="space-y-2">
          <label htmlFor="topic-select" className="block text-sm font-bold text-gray-700">
            Select ROS Topic:
          </label>
          <select
            id="topic-select"
            value={selectedTopic?.name || ''}
            onChange={handleTopicSelect}
            className="w-full p-3 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            <option value="" disabled={isPlaying}>
              {isPlaying ? "🔒 Cannot change topic while playing" : "-- Choose a topic to start streaming --"}
            </option>
            {topics.map((topic) => (
              <option key={topic.name} value={topic.name}>
                {topic.name} ({topic.type}) - {topic.messageCount.toLocaleString()} messages
              </option>
            ))}
          </select>
        </div>

        {/* Topic Info */}
        {selectedTopic && (
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-bold text-gray-800 mb-3">📊 Topic Information</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Type:</span>
                <span className="ml-2 font-mono text-blue-600 font-bold">{selectedTopic.type}</span>
              </div>
              <div>
                <span className="text-gray-600">Frequency:</span>
                <span className="ml-2 font-bold text-purple-600">{selectedTopic.frequency.toFixed(2)} Hz</span>
              </div>
              <div>
                <span className="text-gray-600">Messages:</span>
                <span className="ml-2 font-bold text-green-600">{selectedTopic.messageCount.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-600">Duration:</span>
                <span className="ml-2 font-bold text-orange-600">
                  {((selectedTopic.lastTimestamp - selectedTopic.firstTimestamp) / 1000).toFixed(1)}s
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Message Display */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="font-bold text-gray-800">💬 Live Message Stream</h4>
            <div className="flex items-center space-x-3">
              {messageLoading && (
                <div className="flex items-center space-x-2 text-blue-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-xs">Loading...</span>
                </div>
              )}
              {isPlaying && (
                <div className="flex items-center space-x-2 px-3 py-1 bg-red-100 text-red-700 rounded-full">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-bold">STREAMING</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
            {!selectedTopic ? (
              <div className="p-8 text-center text-gray-400">
                Select a topic to view live messages
              </div>
            ) : !currentMessage ? (
              <div className="p-8 text-center text-gray-400">
                {messageLoading ? 'Loading message...' : 'No message at current time'}
              </div>
            ) : (
              <div className="p-4">
                {/* Message Header */}
                <div className="mb-3 pb-3 border-b border-gray-600">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-blue-400 font-mono">
                      {formatTimestamp(currentMessage.timestamp)}
                    </span>
                    {currentMessage.sequenceNumber && (
                      <span className="text-purple-400 font-mono">
                        Seq: {currentMessage.sequenceNumber}
                      </span>
                    )}
                  </div>
                </div>

                {/* Message Data */}
                <div className="max-h-64 overflow-auto">
                  <pre className="text-sm font-mono text-green-400 whitespace-pre-wrap break-words">
                    {formatMessageData(currentMessage.data)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status */}
        {selectedTopic && currentMessage && (
          <div className="text-center text-sm text-gray-500">
            🎯 Streaming {selectedTopic.name} • Time: {currentTime.toFixed(0)}
          </div>
        )}
      </div>
    </div>
  );
};

export default TopicReader;
