import React, { useState, useEffect, useCallback } from 'react';

export interface ReadableTopic {
  name: string;
  type: string;
  frequency: number;
  messageCount: number;
  firstTimestamp: number;
  lastTimestamp: number;
}

export interface TopicMessage {
  timestamp: number;
  data: any;
  sequenceNumber?: number;
}

export interface TopicReaderProps {
  currentTime: number;
  isPlaying: boolean;
  onTopicSelect?: (topic: ReadableTopic | null) => void;
}

const TopicReader: React.FC<TopicReaderProps> = ({
  currentTime,
  isPlaying,
  onTopicSelect
}) => {
  const [topics, setTopics] = useState<ReadableTopic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<ReadableTopic | null>(null);
  const [currentMessage, setCurrentMessage] = useState<TopicMessage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messageLoading, setMessageLoading] = useState(false);

  // Fetch available topics
  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:3001/api/topics/readable');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setTopics(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching topics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch topics');
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, []);

  // Fetch message at current time when time changes or topic changes
  useEffect(() => {
    if (!selectedTopic || currentTime === 0) {
      setCurrentMessage(null);
      return;
    }

    const fetchMessage = async () => {
      try {
        setMessageLoading(true);
        const encodedTopicName = encodeURIComponent(selectedTopic.name);
        const url = `http://localhost:3001/api/topics/${encodedTopicName}/message?timestamp=${currentTime}`;
        console.log('Fetching message:', url);
        const response = await fetch(url);

        if (response.ok) {
          const message = await response.json();
          setCurrentMessage(message);
        } else if (response.status === 404) {
          // No message found at this timestamp
          setCurrentMessage(null);
        } else {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      } catch (err) {
        console.error('Error fetching message:', err);
        setCurrentMessage(null);
      } finally {
        setMessageLoading(false);
      }
    };

    // Debounce the fetch to avoid too many requests
    const timeoutId = setTimeout(fetchMessage, 100);
    return () => clearTimeout(timeoutId);
  }, [selectedTopic, currentTime]);

  // Handle topic selection
  const handleTopicSelect = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const topicName = event.target.value;
    const topic = topics.find(t => t.name === topicName) || null;
    setSelectedTopic(topic);
    setCurrentMessage(null);

    if (onTopicSelect) {
      onTopicSelect(topic);
    }
  }, [topics, onTopicSelect]);

  // Format JSON data for display
  const formatMessageData = useCallback((data: any): string => {
    if (data === null || data === undefined) {
      return 'null';
    }

    try {
      return JSON.stringify(data, null, 2);
    } catch (err) {
      return String(data);
    }
  }, []);

  // Format timestamp for display
  const formatTimestamp = useCallback((timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toISOString();
  }, []);

  if (loading) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-md border">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-md border">
        <div className="text-red-600">
          <h3 className="font-semibold mb-2">Error Loading Topics</h3>
          <p className="text-sm">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border">
      <div className="space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">Topic Reader</h3>
          <div className="text-sm text-gray-500">
            {topics.length} topics available
          </div>
        </div>

        {/* Topic Selection */}
        <div>
          <label htmlFor="topic-select" className="block text-sm font-medium text-gray-700 mb-2">
            Select Topic:
          </label>
          <select
            id="topic-select"
            value={selectedTopic?.name || ''}
            onChange={handleTopicSelect}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">-- Select a topic --</option>
            {topics.map((topic) => (
              <option key={topic.name} value={topic.name}>
                {topic.name} ({topic.type}) - {topic.messageCount} messages
              </option>
            ))}
          </select>
        </div>

        {/* Topic Info */}
        {selectedTopic && (
          <div className="bg-gray-50 p-3 rounded-md">
            <h4 className="font-medium text-gray-800 mb-2">Topic Information</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="text-gray-600">Type:</span>
                <span className="ml-2 font-mono text-blue-600">{selectedTopic.type}</span>
              </div>
              <div>
                <span className="text-gray-600">Frequency:</span>
                <span className="ml-2">{selectedTopic.frequency.toFixed(2)} Hz</span>
              </div>
              <div>
                <span className="text-gray-600">Messages:</span>
                <span className="ml-2">{selectedTopic.messageCount.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-gray-600">Duration:</span>
                <span className="ml-2">
                  {((selectedTopic.lastTimestamp - selectedTopic.firstTimestamp) / 1000).toFixed(1)}s
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Message Display */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-800">
              Message Content
              {isPlaying && (
                <span className="ml-2 text-xs text-green-600 animate-pulse">● LIVE</span>
              )}
            </h4>
            {messageLoading && (
              <div className="text-xs text-blue-600">Loading...</div>
            )}
          </div>

          <div className="border border-gray-200 rounded-md">
            {!selectedTopic ? (
              <div className="p-4 text-center text-gray-500">
                Select a topic to view messages
              </div>
            ) : !currentMessage ? (
              <div className="p-4 text-center text-gray-500">
                {messageLoading ? 'Loading message...' : 'No message at current time'}
              </div>
            ) : (
              <div className="p-4">
                {/* Message Header */}
                <div className="mb-3 pb-2 border-b border-gray-200">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">
                      Timestamp: <span className="font-mono">{formatTimestamp(currentMessage.timestamp)}</span>
                    </span>
                    {currentMessage.sequenceNumber && (
                      <span className="text-gray-600">
                        Seq: <span className="font-mono">{currentMessage.sequenceNumber}</span>
                      </span>
                    )}
                  </div>
                </div>

                {/* Message Data */}
                <div className="max-h-64 overflow-auto">
                  <pre className="text-xs font-mono text-gray-800 whitespace-pre-wrap break-words">
                    {formatMessageData(currentMessage.data)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status */}
        <div className="text-xs text-gray-500 text-center">
          {selectedTopic && currentMessage && (
            <span>
              Showing message from {selectedTopic.name} at time {currentTime.toFixed(0)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopicReader;
