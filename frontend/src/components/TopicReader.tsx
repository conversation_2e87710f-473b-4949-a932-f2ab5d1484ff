import React, { useState, useEffect, useCallback } from 'react';

export interface ReadableTopic {
  name: string;
  type: string;
  frequency: number;
  messageCount: number;
  firstTimestamp: number;
  lastTimestamp: number;
}

export interface TopicMessage {
  timestamp: number;
  data: any;
  sequenceNumber?: number;
}

export interface TopicReaderProps {
  currentTime: number;
  isPlaying: boolean;
  onTopicSelect?: (topic: ReadableTopic | null) => void;
}

const TopicReader: React.FC<TopicReaderProps> = ({
  currentTime,
  isPlaying,
  onTopicSelect
}) => {
  const [topics, setTopics] = useState<ReadableTopic[]>([]);
  const [selectedTopic, setSelectedTopic] = useState<ReadableTopic | null>(null);
  const [currentMessage, setCurrentMessage] = useState<TopicMessage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messageLoading, setMessageLoading] = useState(false);

  // Fetch available topics
  useEffect(() => {
    const fetchTopics = async () => {
      try {
        setLoading(true);
        const response = await fetch('http://localhost:3001/api/topics/readable');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        setTopics(data);
        setError(null);
      } catch (err) {
        console.error('Error fetching topics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch topics');
      } finally {
        setLoading(false);
      }
    };

    fetchTopics();
  }, []);

  // Fetch message at current time when time changes or topic changes
  useEffect(() => {
    if (!selectedTopic || currentTime === 0) {
      setCurrentMessage(null);
      return;
    }

    const fetchMessage = async () => {
      try {
        setMessageLoading(true);
        const encodedTopicName = encodeURIComponent(selectedTopic.name);
        const url = `http://localhost:3001/api/topics/${encodedTopicName}/message?timestamp=${currentTime}`;
        const response = await fetch(url);

        if (response.ok) {
          const message = await response.json();
          setCurrentMessage(message);
        } else if (response.status === 404) {
          // No message found at this timestamp
          setCurrentMessage(null);
        } else {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      } catch (err) {
        console.error('Error fetching message:', err);
        setCurrentMessage(null);
      } finally {
        setMessageLoading(false);
      }
    };

    // For real-time streaming during playback, fetch immediately
    // For manual seeking, add small debounce
    if (isPlaying) {
      fetchMessage(); // Immediate fetch during playback
    } else {
      const timeoutId = setTimeout(fetchMessage, 50); // Small debounce when paused
      return () => clearTimeout(timeoutId);
    }
  }, [selectedTopic, currentTime, isPlaying]);

  // Handle topic selection
  const handleTopicSelect = useCallback((event: React.ChangeEvent<HTMLSelectElement>) => {
    const topicName = event.target.value;
    const topic = topics.find(t => t.name === topicName) || null;
    setSelectedTopic(topic);
    setCurrentMessage(null);

    if (onTopicSelect) {
      onTopicSelect(topic);
    }
  }, [topics, onTopicSelect]);

  // Format JSON data for display
  const formatMessageData = useCallback((data: any): string => {
    if (data === null || data === undefined) {
      return 'null';
    }

    try {
      return JSON.stringify(data, null, 2);
    } catch (err) {
      return String(data);
    }
  }, []);

  // Format timestamp for display
  const formatTimestamp = useCallback((timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toISOString();
  }, []);

  if (loading) {
    return (
      <div className="bg-gradient-to-br from-white to-slate-50 p-6 rounded-2xl shadow-xl border border-slate-200/50">
        <div className="animate-pulse space-y-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-slate-200 rounded-full"></div>
            <div className="h-6 bg-slate-200 rounded w-1/3"></div>
          </div>
          <div className="h-12 bg-slate-200 rounded-xl"></div>
          <div className="h-48 bg-slate-200 rounded-xl"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-2xl shadow-xl border border-red-200">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h3 className="text-xl font-bold text-red-800 mb-2">Error Loading Topics</h3>
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-red-500 text-white rounded-xl font-medium hover:bg-red-600 transition-colors shadow-lg"
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-white to-slate-50 p-6 rounded-2xl shadow-xl border border-slate-200/50">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Topic Reader
              </h3>
              <p className="text-slate-500 text-sm">Real-time ROS message streaming</p>
            </div>
          </div>
          <div className="flex items-center space-x-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-full">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span className="font-bold text-sm">{topics.length} topics</span>
          </div>
        </div>

        {/* Topic Selection */}
        <div className="space-y-3">
          <label htmlFor="topic-select" className="block text-sm font-bold text-slate-700 uppercase tracking-wide">
            Select ROS Topic
          </label>
          <div className="relative">
            <select
              id="topic-select"
              value={selectedTopic?.name || ''}
              onChange={handleTopicSelect}
              className="w-full p-4 pr-10 border-2 border-slate-200 rounded-xl focus:ring-4 focus:ring-blue-200 focus:border-blue-500 transition-all duration-200 bg-white text-slate-800 font-medium appearance-none cursor-pointer hover:border-slate-300"
            >
              <option value="">🎯 Choose a topic to start streaming...</option>
              {topics.map((topic) => (
                <option key={topic.name} value={topic.name}>
                  📡 {topic.name} • {topic.type} • {topic.messageCount.toLocaleString()} msgs
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
              <svg className="w-5 h-5 text-slate-400" fill="currentColor" viewBox="0 0 24 24">
                <path d="M7 10l5 5 5-5z"/>
              </svg>
            </div>
          </div>
        </div>

        {/* Topic Info */}
        {selectedTopic && (
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-5 rounded-2xl border border-blue-200">
            <h4 className="font-bold text-slate-800 mb-4 flex items-center space-x-2">
              <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span>Topic Information</span>
            </h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white p-3 rounded-xl border border-blue-100">
                <div className="text-xs text-slate-500 font-medium uppercase tracking-wide mb-1">Message Type</div>
                <div className="font-mono text-blue-700 font-bold">{selectedTopic.type}</div>
              </div>
              <div className="bg-white p-3 rounded-xl border border-purple-100">
                <div className="text-xs text-slate-500 font-medium uppercase tracking-wide mb-1">Frequency</div>
                <div className="font-bold text-purple-700">{selectedTopic.frequency.toFixed(2)} Hz</div>
              </div>
              <div className="bg-white p-3 rounded-xl border border-green-100">
                <div className="text-xs text-slate-500 font-medium uppercase tracking-wide mb-1">Total Messages</div>
                <div className="font-bold text-green-700">{selectedTopic.messageCount.toLocaleString()}</div>
              </div>
              <div className="bg-white p-3 rounded-xl border border-orange-100">
                <div className="text-xs text-slate-500 font-medium uppercase tracking-wide mb-1">Duration</div>
                <div className="font-bold text-orange-700">
                  {((selectedTopic.lastTimestamp - selectedTopic.firstTimestamp) / 1000).toFixed(1)}s
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Message Display */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-bold text-slate-800 flex items-center space-x-2">
              <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span>Live Message Stream</span>
            </h4>
            <div className="flex items-center space-x-3">
              {messageLoading && (
                <div className="flex items-center space-x-2 text-blue-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span className="text-xs font-medium">Loading...</span>
                </div>
              )}
              {isPlaying && (
                <div className="flex items-center space-x-2 px-3 py-1 bg-red-100 text-red-700 rounded-full">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-bold">STREAMING</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-gradient-to-br from-slate-900 to-slate-800 rounded-2xl border border-slate-700 overflow-hidden shadow-2xl">
            {!selectedTopic ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-slate-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </svg>
                </div>
                <p className="text-slate-400 font-medium">Select a topic to view live messages</p>
              </div>
            ) : !currentMessage ? (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  {messageLoading ? (
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
                  ) : (
                    <svg className="w-8 h-8 text-slate-400" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  )}
                </div>
                <p className="text-slate-400 font-medium">
                  {messageLoading ? 'Loading message...' : 'No message at current time'}
                </p>
              </div>
            ) : (
              <div className="p-6">
                {/* Message Header */}
                <div className="mb-4 pb-4 border-b border-slate-600">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-4">
                      <div className="px-3 py-1 bg-blue-600 text-white rounded-full text-xs font-bold">
                        {selectedTopic.name}
                      </div>
                      <div className="text-slate-300 text-sm font-mono">
                        {formatTimestamp(currentMessage.timestamp)}
                      </div>
                    </div>
                    {currentMessage.sequenceNumber && (
                      <div className="px-3 py-1 bg-purple-600 text-white rounded-full text-xs font-bold">
                        Seq: {currentMessage.sequenceNumber}
                      </div>
                    )}
                  </div>
                </div>

                {/* Message Data */}
                <div className="max-h-80 overflow-auto">
                  <pre className="text-sm font-mono text-green-400 whitespace-pre-wrap break-words leading-relaxed">
                    {formatMessageData(currentMessage.data)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Status */}
        {selectedTopic && currentMessage && (
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 px-4 py-2 bg-slate-100 text-slate-600 rounded-full text-sm">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span className="font-medium">
                Streaming {selectedTopic.name} • Time: {currentTime.toFixed(0)}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TopicReader;
