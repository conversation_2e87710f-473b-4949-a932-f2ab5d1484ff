import React, { useState, useEffect, useRef, useCallback } from 'react';
import type { LogMessage } from '../types/logs';
import { getLogLevelName, getLogLevelColor, getLogLevelBgColor } from '../types/logs';

export interface LogConsoleProps {
  currentTime: number;
  isPlaying: boolean;
}

const LogConsole: React.FC<LogConsoleProps> = ({ currentTime, isPlaying }) => {
  const [logs, setLogs] = useState<LogMessage[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [autoScroll, setAutoScroll] = useState(true);
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const consoleRef = useRef<HTMLDivElement>(null);
  const lastFetchTime = useRef<number>(0);

  // Format timestamp for display
  const formatTimestamp = useCallback((timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      fractionalSecondDigits: 3
    });
  }, []);

  // Fetch logs from backend
  const fetchLogs = useCallback(async (timestamp: number, search: string = '') => {
    // Throttle requests to avoid overwhelming the server
    const now = Date.now();
    if (now - lastFetchTime.current < 100) { // 100ms throttle
      return;
    }
    lastFetchTime.current = now;

    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams({
        timestamp: timestamp.toString(),
        ...(search && { search })
      });

      const response = await fetch(`http://localhost:3001/api/logs?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const logsData = await response.json();
      setLogs(logsData);
    } catch (err) {
      console.error('Error fetching logs:', err);
      setError('Failed to load logs. Make sure the backend server is running.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch logs when currentTime or searchTerm changes
  useEffect(() => {
    if (currentTime > 0) {
      fetchLogs(currentTime, searchTerm);
    }
  }, [currentTime, searchTerm, fetchLogs]);

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (autoScroll && consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [logs, autoScroll]);

  // Handle manual scroll - disable auto-scroll if user scrolls up
  const handleScroll = useCallback(() => {
    if (consoleRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = consoleRef.current;
      const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10; // 10px tolerance
      setAutoScroll(isAtBottom);
    }
  }, []);

  // Copy log message to clipboard
  const copyToClipboard = useCallback(async (log: LogMessage, index: number) => {
    const logText = `[${formatTimestamp(log.timestamp)}] [${getLogLevelName(log.level)}] ${log.name}: ${log.msg}`;

    try {
      await navigator.clipboard.writeText(logText);
      setCopiedIndex(index);
      setTimeout(() => setCopiedIndex(null), 2000); // Clear after 2 seconds
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  }, [formatTimestamp]);

  // Highlight search terms in text
  const highlightSearchTerm = useCallback((text: string, searchTerm: string): React.ReactNode => {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-yellow-300 text-black font-bold">
          {part}
        </span>
      ) : (
        part
      )
    );
  }, []);

  return (
    <div className="bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl shadow-xl p-6 border-2 border-slate-200">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-slate-600 rounded-xl flex items-center justify-center">
            <span className="text-2xl">📜</span>
          </div>
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-600 to-gray-600 bg-clip-text text-transparent">
              ROS Log Console
            </h2>
            <p className="text-slate-500 text-sm">
              Live logs from /rosout_agg • {logs.length} messages
            </p>
          </div>
        </div>

        {/* Auto-scroll toggle */}
        <button
          onClick={() => setAutoScroll(!autoScroll)}
          className={`px-3 py-1 rounded-lg text-xs font-medium transition-colors ${
            autoScroll
              ? 'bg-green-100 text-green-700 border border-green-300'
              : 'bg-gray-100 text-gray-600 border border-gray-300'
          }`}
        >
          {autoScroll ? '📍 Auto-scroll ON' : '📍 Auto-scroll OFF'}
        </button>
      </div>

      {/* Search Input */}
      <div className="mb-4">
        <div className="relative">
          <input
            type="text"
            placeholder="🔍 Search logs by message or node name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full p-3 pl-10 border-2 border-gray-300 rounded-lg focus:ring-2 focus:ring-slate-500 focus:border-slate-500 bg-white"
          />
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            🔍
          </div>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <span className="text-red-600 mr-2">⚠️</span>
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      {/* Console Output */}
      <div
        ref={consoleRef}
        onScroll={handleScroll}
        className="bg-gray-900 rounded-lg border border-gray-700 h-96 overflow-auto font-mono text-sm"
      >
        {isLoading && logs.length === 0 ? (
          <div className="p-8 text-center text-gray-400">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400 mx-auto mb-4"></div>
            Loading logs...
          </div>
        ) : logs.length === 0 ? (
          <div className="p-8 text-center text-gray-400">
            {searchTerm ? `No logs found matching "${searchTerm}"` : 'No logs available at current time'}
          </div>
        ) : (
          <div className="p-4 space-y-1">
            {logs.map((log, index) => (
              <div
                key={`${log.timestamp}-${index}`}
                className="group hover:bg-gray-800 p-2 rounded transition-colors cursor-pointer"
                onClick={() => copyToClipboard(log, index)}
                title="Click to copy to clipboard"
              >
                <div className="flex items-start space-x-3">
                  {/* Timestamp */}
                  <span className="text-gray-500 text-xs whitespace-nowrap">
                    {formatTimestamp(log.timestamp)}
                  </span>

                  {/* Log Level */}
                  <span className={`text-xs font-bold whitespace-nowrap ${getLogLevelColor(log.level)}`}>
                    [{getLogLevelName(log.level)}]
                  </span>

                  {/* Node Name */}
                  <span className="text-purple-400 text-xs whitespace-nowrap">
                    {highlightSearchTerm(log.name, searchTerm)}
                  </span>

                  {/* Message */}
                  <span className="text-green-400 flex-1 break-words">
                    {highlightSearchTerm(log.msg, searchTerm)}
                  </span>

                  {/* Copy indicator */}
                  {copiedIndex === index && (
                    <span className="text-yellow-400 text-xs">📋 Copied!</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Status Bar */}
      <div className="mt-4 flex justify-between items-center text-xs text-gray-500">
        <div className="flex items-center space-x-4">
          <span>🕒 Time: {currentTime.toFixed(0)}</span>
          <span className={isPlaying ? 'text-green-600' : 'text-gray-500'}>
            {isPlaying ? '▶️ Playing' : '⏸️ Paused'}
          </span>
          {searchTerm && (
            <span className="text-blue-600">
              🔍 Filtered: "{searchTerm}"
            </span>
          )}
        </div>
        <div>
          Click any log entry to copy to clipboard
        </div>
      </div>
    </div>
  );
};

export default LogConsole;
