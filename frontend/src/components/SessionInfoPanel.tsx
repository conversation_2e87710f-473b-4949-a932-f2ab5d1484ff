import type { FC } from 'react';

/**
 * Interface for session information props
 */
export interface SessionInfo {
  sessionId?: string;
  timestamp?: string;
  duration?: number;
  startTime?: string;
  endTime?: string;
  version?: string;
  [key: string]: any; // Allow additional session properties
}

interface SessionInfoPanelProps {
  sessionInfo: SessionInfo;
  isLoading?: boolean;
}

/**
 * SessionInfoPanel component displays session metadata in a structured format
 * @param sessionInfo - Session metadata object
 * @param isLoading - Loading state indicator
 */
export const SessionInfoPanel: FC<SessionInfoPanelProps> = ({
  sessionInfo,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl shadow-xl p-6 border-2 border-blue-200">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
            <span className="text-2xl">🔧</span>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Session Information</h2>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-blue-200 rounded-lg w-3/4"></div>
          <div className="h-4 bg-blue-200 rounded-lg w-1/2"></div>
          <div className="h-4 bg-blue-200 rounded-lg w-2/3"></div>
          <div className="h-4 bg-blue-200 rounded-lg w-4/5"></div>
        </div>
      </div>
    );
  }

  // Check if session info is empty
  const hasSessionData = Object.keys(sessionInfo).length > 0;

  return (
    <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl shadow-xl p-6 border-2 border-blue-200">
      <div className="flex items-center mb-6">
        <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
          <span className="text-2xl">🔧</span>
        </div>
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Session Information</h2>
      </div>

      {!hasSessionData ? (
        <div className="text-slate-500 italic text-center py-8">
          No session information available
        </div>
      ) : (
        <div className="space-y-4">
          {/* Primary session fields */}
          {sessionInfo.sessionId && (
            <div className="bg-white p-4 rounded-xl border border-blue-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Session ID:</span>
              <span className="text-blue-700 font-mono text-sm bg-blue-100 px-3 py-1 rounded-lg">{sessionInfo.sessionId}</span>
            </div>
          )}

          {sessionInfo.timestamp && (
            <div className="bg-white p-4 rounded-xl border border-blue-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Timestamp:</span>
              <span className="text-slate-800 font-mono text-sm">{sessionInfo.timestamp}</span>
            </div>
          )}

          {sessionInfo.startTime && (
            <div className="bg-white p-4 rounded-xl border border-green-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Start Time:</span>
              <span className="text-green-700 font-mono text-sm">{sessionInfo.startTime}</span>
            </div>
          )}

          {sessionInfo.endTime && (
            <div className="bg-white p-4 rounded-xl border border-red-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">End Time:</span>
              <span className="text-red-700 font-mono text-sm">{sessionInfo.endTime}</span>
            </div>
          )}

          {sessionInfo.duration !== undefined && (
            <div className="bg-white p-4 rounded-xl border border-purple-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Duration:</span>
              <span className="text-purple-700 font-bold">
                {typeof sessionInfo.duration === 'number'
                  ? `${(sessionInfo.duration / 1000).toFixed(1)}s`
                  : sessionInfo.duration}
              </span>
            </div>
          )}

          {sessionInfo.version && (
            <div className="bg-white p-4 rounded-xl border border-orange-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Version:</span>
              <span className="text-orange-700 font-mono text-sm bg-orange-100 px-3 py-1 rounded-lg">{sessionInfo.version}</span>
            </div>
          )}

          {/* Additional session properties */}
          {Object.entries(sessionInfo)
            .filter(([key]) => !['sessionId', 'timestamp', 'startTime', 'endTime', 'duration', 'version'].includes(key))
            .map(([key, value]) => (
              <div key={key} className="bg-white p-4 rounded-xl border border-slate-200 flex justify-between items-center">
                <span className="font-bold text-slate-700 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                </span>
                <span className="text-slate-600 text-sm font-medium">
                  {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                </span>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};
