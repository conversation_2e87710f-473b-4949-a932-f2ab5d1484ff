import React, { useState, useEffect, useCallback } from 'react';

export interface PlaybackTimeRange {
  start: number;
  end: number;
  duration: number;
}

export interface ReadableTopic {
  name: string;
  type: string;
  frequency: number;
  messageCount: number;
  firstTimestamp: number;
  lastTimestamp: number;
}

export interface PlaybackControlProps {
  timeRange: PlaybackTimeRange | null;
  currentTime: number;
  isPlaying: boolean;
  playbackSpeed: number;
  onTimeChange: (time: number) => void;
  onPlayPause: () => void;
  onSpeedChange: (speed: number) => void;
  selectedTopic: ReadableTopic | null;
}

const PlaybackControl: React.FC<PlaybackControlProps> = ({
  timeRange,
  currentTime,
  isPlaying,
  playbackSpeed,
  onTimeChange,
  onPlayPause,
  onSpeedChange,
  selectedTopic
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [localTime, setLocalTime] = useState(currentTime);

  // Update local time when currentTime changes (unless user is dragging)
  useEffect(() => {
    if (!isDragging) {
      setLocalTime(currentTime);
    }
  }, [currentTime, isDragging]);

  // Format timestamp to readable time
  const formatTime = useCallback((timestamp: number): string => {
    if (!timeRange) return '00:00';
    const relativeTime = (timestamp - timeRange.start) / 1000;
    const minutes = Math.floor(relativeTime / 60);
    const seconds = Math.floor(relativeTime % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRange]);

  // Format duration
  const formatDuration = useCallback((): string => {
    if (!timeRange) return '00:00';
    const totalSeconds = timeRange.duration / 1000;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRange]);

  // Calculate progress percentage
  const getProgressPercentage = useCallback((): number => {
    if (!timeRange) return 0;
    const progress = (localTime - timeRange.start) / timeRange.duration;
    return Math.max(0, Math.min(100, progress * 100));
  }, [localTime, timeRange]);

  // Handle slider change
  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!timeRange) return;
    const percentage = parseFloat(event.target.value);
    const newTime = timeRange.start + (percentage / 100) * timeRange.duration;
    setLocalTime(newTime);
    setIsDragging(true);
  };

  // Handle slider mouse up (commit the change)
  const handleSliderMouseUp = () => {
    if (isDragging) {
      onTimeChange(localTime);
      setIsDragging(false);
    }
  };

  // Speed options
  const speedOptions = [0.25, 0.5, 1, 2, 5];

  // Check if topic is selected
  const isTopicSelected = selectedTopic !== null;

  if (!timeRange) {
    return (
      <div className="bg-red-500 p-8 rounded-xl border-4 border-yellow-400 shadow-2xl">
        <div className="flex items-center justify-center space-x-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-4 border-white"></div>
          <span className="text-white font-bold text-xl">Loading playback controls...</span>
        </div>
      </div>
    );
  }

  // Show message when no topic is selected
  if (!isTopicSelected) {
    return (
      <div className="bg-gradient-to-r from-slate-100 to-slate-200 p-8 rounded-2xl shadow-2xl border-2 border-slate-300 opacity-75">
        <div className="text-center space-y-4">
          <div className="text-6xl">🎯</div>
          <h3 className="text-2xl font-bold text-slate-600">Select a Topic to Enable Playback</h3>
          <p className="text-slate-500 text-lg">
            Choose a ROS topic from the dropdown below to start streaming and enable playback controls.
          </p>
          <div className="flex items-center justify-center space-x-2 text-slate-400">
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
            <span className="font-medium">Playback controls disabled</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-r from-blue-100 to-purple-100 p-8 rounded-2xl shadow-2xl border-2 border-blue-300">
      <div className="space-y-8">
        {/* Time Display */}
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span className="font-mono font-bold text-lg">{formatTime(localTime)}</span>
          <span className="text-xs text-gray-400">Duration: {formatDuration()}</span>
          <span className="font-mono font-bold text-lg">{formatTime(timeRange.end)}</span>
        </div>

        {/* Progress Bar */}
        <div className="relative">
          <div className="w-full h-3 bg-gray-200 rounded-lg relative overflow-hidden">
            <div
              className="h-full bg-blue-500 rounded-lg transition-all duration-100"
              style={{ width: `${getProgressPercentage()}%` }}
            ></div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            step="0.1"
            value={getProgressPercentage()}
            onChange={handleSliderChange}
            onMouseUp={handleSliderMouseUp}
            onTouchEnd={handleSliderMouseUp}
            className="absolute top-0 w-full h-3 opacity-0 cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-2">
            <span>Start</span>
            <span className="font-bold">{getProgressPercentage().toFixed(1)}%</span>
            <span>End</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          {/* Play/Pause Button */}
          <button
            onClick={onPlayPause}
            className={`flex items-center justify-center w-20 h-20 rounded-full transition-all duration-300 shadow-2xl border-4 transform hover:scale-110 ${
              isPlaying
                ? 'bg-red-500 hover:bg-red-600 text-white border-red-700 animate-pulse'
                : 'bg-green-500 hover:bg-green-600 text-white border-green-700'
            }`}
            title={isPlaying ? 'Pause' : 'Play'}
          >
            {isPlaying ? (
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
              </svg>
            ) : (
              <svg className="w-8 h-8 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            )}
          </button>

          {/* Status */}
          <div className="text-center">
            <div className={`font-bold text-lg ${isPlaying ? 'text-red-600' : 'text-green-600'}`}>
              {isPlaying ? '⏸️ PLAYING' : '▶️ PAUSED'}
            </div>
            <div className="text-xs text-gray-500">
              Speed: {playbackSpeed}x
            </div>
          </div>

          {/* Speed Control */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600 font-medium">Speed:</span>
            <div className="flex space-x-1">
              {speedOptions.map((speed) => (
                <button
                  key={speed}
                  onClick={() => onSpeedChange(speed)}
                  className={`px-3 py-2 text-sm font-bold rounded-lg transition-colors border-2 ${
                    playbackSpeed === speed
                      ? 'bg-blue-500 text-white border-blue-600 shadow-md'
                      : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                  }`}
                >
                  {speed}x
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Seeking Info */}
        {isDragging && (
          <div className="text-center text-blue-600 text-sm font-medium">
            🎯 Seeking to {formatTime(localTime)}
          </div>
        )}
      </div>
    </div>
  );
};

export default PlaybackControl;
