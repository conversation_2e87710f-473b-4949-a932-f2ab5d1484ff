import type { FC } from 'react';

/**
 * Interface for robot information props
 */
export interface RobotInfo {
  robotId?: string;
  robotName?: string;
  model?: string;
  firmware?: string;
  serialNumber?: string;
  capabilities?: string[];
  sensors?: string[];
  [key: string]: any; // Allow additional robot properties
}

interface RobotInfoPanelProps {
  robotInfo: RobotInfo;
  isLoading?: boolean;
}

/**
 * RobotInfoPanel component displays robot metadata in a structured format
 * @param robotInfo - Robot metadata object
 * @param isLoading - Loading state indicator
 */
export const RobotInfoPanel: FC<RobotInfoPanelProps> = ({
  robotInfo,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl shadow-xl p-6 border-2 border-green-200">
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
            <span className="text-2xl">🤖</span>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Robot Information</h2>
        </div>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-green-200 rounded-lg w-3/4"></div>
          <div className="h-4 bg-green-200 rounded-lg w-1/2"></div>
          <div className="h-4 bg-green-200 rounded-lg w-2/3"></div>
          <div className="h-4 bg-green-200 rounded-lg w-4/5"></div>
        </div>
      </div>
    );
  }

  // Check if robot info is empty
  const hasRobotData = Object.keys(robotInfo).length > 0;

  return (
    <div className="bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl shadow-xl p-6 border-2 border-green-200">
      <div className="flex items-center mb-6">
        <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
          <span className="text-2xl">🤖</span>
        </div>
        <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">Robot Information</h2>
      </div>

      {!hasRobotData ? (
        <div className="text-slate-500 italic text-center py-8">
          No robot information available
        </div>
      ) : (
        <div className="space-y-4">
          {/* Primary robot fields */}
          {robotInfo.robotId && (
            <div className="bg-white p-4 rounded-xl border border-green-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Robot ID:</span>
              <span className="text-green-700 font-mono text-sm bg-green-100 px-3 py-1 rounded-lg">{robotInfo.robotId}</span>
            </div>
          )}

          {robotInfo.robotName && (
            <div className="bg-white p-4 rounded-xl border border-blue-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Robot Name:</span>
              <span className="text-blue-700 font-bold">{robotInfo.robotName}</span>
            </div>
          )}

          {robotInfo.model && (
            <div className="bg-white p-4 rounded-xl border border-purple-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Model:</span>
              <span className="text-purple-700 font-bold">{robotInfo.model}</span>
            </div>
          )}

          {robotInfo.firmware && (
            <div className="bg-white p-4 rounded-xl border border-orange-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Firmware:</span>
              <span className="text-orange-700 font-mono text-sm bg-orange-100 px-3 py-1 rounded-lg">{robotInfo.firmware}</span>
            </div>
          )}

          {robotInfo.serialNumber && (
            <div className="bg-white p-4 rounded-xl border border-red-200 flex justify-between items-center">
              <span className="font-bold text-slate-700">Serial Number:</span>
              <span className="text-red-700 font-mono text-sm bg-red-100 px-3 py-1 rounded-lg">{robotInfo.serialNumber}</span>
            </div>
          )}

          {/* Array fields with special handling */}
          {robotInfo.capabilities && robotInfo.capabilities.length > 0 && (
            <div className="bg-white p-4 rounded-xl border border-blue-200">
              <span className="font-bold text-slate-700 block mb-3">🚀 Capabilities:</span>
              <div className="flex flex-wrap gap-2">
                {robotInfo.capabilities.map((capability, index) => (
                  <span
                    key={index}
                    className="px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg shadow-sm"
                  >
                    {capability}
                  </span>
                ))}
              </div>
            </div>
          )}

          {robotInfo.sensors && robotInfo.sensors.length > 0 && (
            <div className="bg-white p-4 rounded-xl border border-green-200">
              <span className="font-bold text-slate-700 block mb-3">📡 Sensors:</span>
              <div className="flex flex-wrap gap-2">
                {robotInfo.sensors.map((sensor, index) => (
                  <span
                    key={index}
                    className="px-3 py-2 bg-green-500 text-white text-sm font-medium rounded-lg shadow-sm"
                  >
                    {sensor}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Additional robot properties */}
          {Object.entries(robotInfo)
            .filter(([key]) => !['robotId', 'robotName', 'model', 'firmware', 'serialNumber', 'capabilities', 'sensors'].includes(key))
            .map(([key, value]) => (
              <div key={key} className="bg-white p-4 rounded-xl border border-slate-200 flex justify-between items-center">
                <span className="font-bold text-slate-700 capitalize">
                  {key.replace(/([A-Z])/g, ' $1').trim()}:
                </span>
                <span className="text-slate-600 text-sm font-medium">
                  {Array.isArray(value) ? (
                    <div className="flex flex-wrap gap-1">
                      {value.map((item, index) => (
                        <span key={index} className="px-2 py-1 bg-slate-100 text-slate-700 text-xs rounded-lg">
                          {String(item)}
                        </span>
                      ))}
                    </div>
                  ) : typeof value === 'object' ? (
                    JSON.stringify(value)
                  ) : (
                    String(value)
                  )}
                </span>
              </div>
            ))}
        </div>
      )}
    </div>
  );
};
