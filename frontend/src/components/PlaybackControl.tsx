import React, { useState, useEffect, useCallback } from 'react';

export interface PlaybackTimeRange {
  start: number;
  end: number;
  duration: number;
}

export interface PlaybackControlProps {
  timeRange: PlaybackTimeRange | null;
  currentTime: number;
  isPlaying: boolean;
  playbackSpeed: number;
  onTimeChange: (time: number) => void;
  onPlayPause: () => void;
  onSpeedChange: (speed: number) => void;
}

const PlaybackControl: React.FC<PlaybackControlProps> = ({
  timeRange,
  currentTime,
  isPlaying,
  playbackSpeed,
  onTimeChange,
  onPlayPause,
  onSpeedChange
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [localTime, setLocalTime] = useState(currentTime);

  // Update local time when currentTime changes (unless user is dragging)
  useEffect(() => {
    if (!isDragging) {
      setLocalTime(currentTime);
    }
  }, [currentTime, isDragging]);

  // Format timestamp to readable time
  const formatTime = useCallback((timestamp: number): string => {
    if (!timeRange) return '00:00';

    // Convert to relative time from start
    const relativeTime = (timestamp - timeRange.start) / 1000; // Convert to seconds
    const minutes = Math.floor(relativeTime / 60);
    const seconds = Math.floor(relativeTime % 60);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRange]);

  // Format duration
  const formatDuration = useCallback((): string => {
    if (!timeRange) return '00:00';

    const totalSeconds = timeRange.duration / 1000;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRange]);

  // Calculate progress percentage
  const getProgressPercentage = useCallback((): number => {
    if (!timeRange) return 0;

    const progress = (localTime - timeRange.start) / timeRange.duration;
    return Math.max(0, Math.min(100, progress * 100));
  }, [localTime, timeRange]);

  // Handle slider change
  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!timeRange) return;

    const percentage = parseFloat(event.target.value);
    const newTime = timeRange.start + (percentage / 100) * timeRange.duration;

    setLocalTime(newTime);
    setIsDragging(true);
  };

  // Handle slider mouse up (commit the change)
  const handleSliderMouseUp = () => {
    if (isDragging) {
      onTimeChange(localTime);
      setIsDragging(false);
    }
  };

  // Speed options
  const speedOptions = [0.25, 0.5, 1, 2, 5];

  if (!timeRange) {
    return (
      <div className="bg-gradient-to-r from-slate-50 to-slate-100 p-6 rounded-2xl border border-slate-200 shadow-lg">
        <div className="flex items-center justify-center space-x-3">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
          <span className="text-slate-600 font-medium">Loading playback controls...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-white to-slate-50 p-6 rounded-2xl shadow-xl border border-slate-200/50 backdrop-blur-sm">
      <div className="space-y-6">
        {/* Time Display */}
        <div className="flex justify-between items-center">
          <div className="flex flex-col items-start">
            <span className="text-xs text-slate-500 font-medium">Current Time</span>
            <span className="text-lg font-mono font-bold text-slate-800">{formatTime(localTime)}</span>
          </div>
          <div className="flex flex-col items-center">
            <span className="text-xs text-slate-500 font-medium">Total Duration</span>
            <span className="text-sm font-mono text-slate-600">{formatDuration()}</span>
          </div>
          <div className="flex flex-col items-end">
            <span className="text-xs text-slate-500 font-medium">End Time</span>
            <span className="text-lg font-mono font-bold text-slate-800">{formatTime(timeRange.end)}</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="relative group">
          <div className="w-full h-3 bg-gradient-to-r from-slate-200 to-slate-300 rounded-full relative overflow-hidden shadow-inner">
            <div
              className="h-full bg-gradient-to-r from-blue-500 via-blue-600 to-purple-600 rounded-full transition-all duration-200 shadow-sm"
              style={{ width: `${getProgressPercentage()}%` }}
            ></div>
            {/* Progress indicator dot */}
            <div
              className="absolute top-1/2 w-5 h-5 bg-white rounded-full border-3 border-blue-500 shadow-lg transform -translate-y-1/2 transition-all duration-200 group-hover:scale-110"
              style={{ left: `calc(${getProgressPercentage()}% - 10px)` }}
            >
              <div className="absolute inset-1 bg-blue-500 rounded-full"></div>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            step="0.1"
            value={getProgressPercentage()}
            onChange={handleSliderChange}
            onMouseUp={handleSliderMouseUp}
            onTouchEnd={handleSliderMouseUp}
            className="absolute top-0 w-full h-3 opacity-0 cursor-pointer"
          />
          <div className="flex justify-between items-center mt-3">
            <span className="text-xs text-slate-500 font-medium">0:00</span>
            <div className="flex items-center space-x-2">
              <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-bold">
                {getProgressPercentage().toFixed(1)}%
              </div>
              {isPlaying && (
                <div className="flex items-center space-x-1 text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium">LIVE</span>
                </div>
              )}
            </div>
            <span className="text-xs text-slate-500 font-medium">{formatDuration()}</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-center space-x-8">
          {/* Play/Pause Button */}
          <button
            onClick={onPlayPause}
            className={`group relative flex items-center justify-center w-20 h-20 rounded-full transition-all duration-300 shadow-2xl transform hover:scale-105 active:scale-95 ${
              isPlaying
                ? 'bg-gradient-to-br from-orange-400 to-red-500 hover:from-orange-500 hover:to-red-600 shadow-red-300'
                : 'bg-gradient-to-br from-green-400 to-blue-500 hover:from-green-500 hover:to-blue-600 shadow-blue-300'
            }`}
            title={isPlaying ? 'Pause Playback' : 'Start Playback'}
          >
            <div className="absolute inset-0 rounded-full bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            {isPlaying ? (
              // Pause icon - two rectangles
              <svg className="w-10 h-10 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
              </svg>
            ) : (
              // Play icon - triangle
              <svg className="w-10 h-10 ml-1 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            )}
          </button>

          {/* Status Display */}
          <div className="flex flex-col items-center space-y-2">
            <div className={`flex items-center space-x-2 px-4 py-2 rounded-full font-bold text-sm ${
              isPlaying
                ? 'bg-red-100 text-red-700 border border-red-200'
                : 'bg-slate-100 text-slate-700 border border-slate-200'
            }`}>
              <div className={`w-3 h-3 rounded-full ${
                isPlaying ? 'bg-red-500 animate-pulse' : 'bg-slate-400'
              }`}></div>
              <span>{isPlaying ? 'PLAYING' : 'PAUSED'}</span>
            </div>
          </div>

          {/* Speed Control */}
          <div className="flex flex-col items-center space-y-3">
            <span className="text-xs text-slate-500 font-medium uppercase tracking-wide">Playback Speed</span>
            <div className="flex space-x-2">
              {speedOptions.map((speed) => (
                <button
                  key={speed}
                  onClick={() => onSpeedChange(speed)}
                  className={`relative px-4 py-2 text-sm font-bold rounded-xl transition-all duration-300 transform hover:scale-105 ${
                    playbackSpeed === speed
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg shadow-purple-300 scale-110'
                      : 'bg-white text-slate-600 border-2 border-slate-200 hover:border-purple-300 hover:text-purple-600 shadow-md'
                  }`}
                >
                  {playbackSpeed === speed && (
                    <div className="absolute inset-0 rounded-xl bg-white/20 animate-pulse"></div>
                  )}
                  <span className="relative">{speed}×</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Additional Info */}
        {isDragging && (
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
              <span>Seeking to {formatTime(localTime)}</span>
            </div>
          </div>
        )}
      </div>


    </div>
  );
};

export default PlaybackControl;
