import React, { useState, useEffect, useCallback } from 'react';

export interface PlaybackTimeRange {
  start: number;
  end: number;
  duration: number;
}

export interface PlaybackControlProps {
  timeRange: PlaybackTimeRange | null;
  currentTime: number;
  isPlaying: boolean;
  playbackSpeed: number;
  onTimeChange: (time: number) => void;
  onPlayPause: () => void;
  onSpeedChange: (speed: number) => void;
}

const PlaybackControl: React.FC<PlaybackControlProps> = ({
  timeRange,
  currentTime,
  isPlaying,
  playbackSpeed,
  onTimeChange,
  onPlayPause,
  onSpeedChange
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [localTime, setLocalTime] = useState(currentTime);

  // Update local time when currentTime changes (unless user is dragging)
  useEffect(() => {
    if (!isDragging) {
      setLocalTime(currentTime);
    }
  }, [currentTime, isDragging]);

  // Format timestamp to readable time
  const formatTime = useCallback((timestamp: number): string => {
    if (!timeRange) return '00:00';

    // Convert to relative time from start
    const relativeTime = (timestamp - timeRange.start) / 1000; // Convert to seconds
    const minutes = Math.floor(relativeTime / 60);
    const seconds = Math.floor(relativeTime % 60);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRange]);

  // Format duration
  const formatDuration = useCallback((): string => {
    if (!timeRange) return '00:00';

    const totalSeconds = timeRange.duration / 1000;
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = Math.floor(totalSeconds % 60);

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRange]);

  // Calculate progress percentage
  const getProgressPercentage = useCallback((): number => {
    if (!timeRange) return 0;

    const progress = (localTime - timeRange.start) / timeRange.duration;
    return Math.max(0, Math.min(100, progress * 100));
  }, [localTime, timeRange]);

  // Handle slider change
  const handleSliderChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!timeRange) return;

    const percentage = parseFloat(event.target.value);
    const newTime = timeRange.start + (percentage / 100) * timeRange.duration;

    setLocalTime(newTime);
    setIsDragging(true);
  };

  // Handle slider mouse up (commit the change)
  const handleSliderMouseUp = () => {
    if (isDragging) {
      onTimeChange(localTime);
      setIsDragging(false);
    }
  };

  // Speed options
  const speedOptions = [0.25, 0.5, 1, 2, 5];

  if (!timeRange) {
    return (
      <div className="bg-gray-100 p-4 rounded-lg">
        <div className="text-center text-gray-500">
          Loading playback controls...
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow-md border">
      <div className="space-y-4">
        {/* Time Display */}
        <div className="flex justify-between items-center text-sm text-gray-600">
          <span>{formatTime(localTime)}</span>
          <span className="text-xs text-gray-400">
            Duration: {formatDuration()}
          </span>
          <span>{formatTime(timeRange.end)}</span>
        </div>

        {/* Progress Bar */}
        <div className="relative">
          <div className="w-full h-4 bg-gray-200 rounded-lg relative overflow-hidden border border-gray-300">
            <div
              className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg transition-all duration-100 shadow-inner"
              style={{ width: `${getProgressPercentage()}%` }}
            ></div>
            {/* Progress indicator dot */}
            <div
              className="absolute top-1/2 w-4 h-4 bg-blue-600 rounded-full border-2 border-white shadow-lg transform -translate-y-1/2 transition-all duration-100"
              style={{ left: `calc(${getProgressPercentage()}% - 8px)` }}
            ></div>
          </div>
          <input
            type="range"
            min="0"
            max="100"
            step="0.1"
            value={getProgressPercentage()}
            onChange={handleSliderChange}
            onMouseUp={handleSliderMouseUp}
            onTouchEnd={handleSliderMouseUp}
            className="absolute top-0 w-full h-4 opacity-0 cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-400 mt-2">
            <span>Start</span>
            <span className="font-mono">{getProgressPercentage().toFixed(1)}%</span>
            <span>End</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          {/* Play/Pause Button */}
          <button
            onClick={onPlayPause}
            className={`flex items-center justify-center w-16 h-16 rounded-full transition-all duration-200 shadow-lg border-2 ${
              isPlaying
                ? 'bg-red-500 hover:bg-red-600 text-white border-red-600 shadow-red-200'
                : 'bg-green-500 hover:bg-green-600 text-white border-green-600 shadow-green-200'
            }`}
            title={isPlaying ? 'Pause' : 'Play'}
          >
            {isPlaying ? (
              // Pause icon - two rectangles
              <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
              </svg>
            ) : (
              // Play icon - triangle
              <svg className="w-8 h-8 ml-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            )}
          </button>

          {/* Button State Debug */}
          <div className="text-xs text-center">
            <div className={`font-bold ${isPlaying ? 'text-red-600' : 'text-green-600'}`}>
              {isPlaying ? '⏸️ PLAYING' : '▶️ PAUSED'}
            </div>
            <div className="text-gray-500">
              State: {isPlaying ? 'true' : 'false'}
            </div>
          </div>

          {/* Speed Control */}
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Speed:</span>
            <div className="flex space-x-1">
              {speedOptions.map((speed) => (
                <button
                  key={speed}
                  onClick={() => onSpeedChange(speed)}
                  className={`px-3 py-2 text-sm rounded-lg font-medium transition-all duration-200 border-2 ${
                    playbackSpeed === speed
                      ? 'bg-blue-500 text-white border-blue-600 shadow-lg shadow-blue-200 scale-105'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                  }`}
                >
                  {speed}x
                </button>
              ))}
            </div>
          </div>

          {/* Current Status */}
          <div className="text-sm text-gray-600">
            {isPlaying ? (
              <span className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                Playing
              </span>
            ) : (
              <span className="flex items-center">
                <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                Paused
              </span>
            )}
          </div>
        </div>

        {/* Additional Info */}
        <div className="text-xs text-gray-500 text-center">
          {isDragging && (
            <span className="text-blue-600">Seeking to {formatTime(localTime)}</span>
          )}
        </div>
      </div>


    </div>
  );
};

export default PlaybackControl;
