import React, { useRef, useEffect, useState, useCallback } from 'react';

export interface CameraViewProps {
  currentTime: number;
  isPlaying: boolean;
  onTimeUpdate?: (time: number) => void;
}

const CameraView: React.FC<CameraViewProps> = ({ 
  currentTime, 
  isPlaying, 
  onTimeUpdate 
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [videoDuration, setVideoDuration] = useState(0);
  const [videoCurrentTime, setVideoCurrentTime] = useState(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  // Convert ION timestamp to video time (assuming video starts at ION start time)
  const ION_START_TIME = 1740105930280; // From the ION file time range
  
  const convertIonTimeToVideoTime = useCallback((ionTime: number): number => {
    return Math.max(0, (ionTime - ION_START_TIME) / 1000); // Convert ms to seconds
  }, []);

  const convertVideoTimeToIonTime = useCallback((videoTime: number): number => {
    return ION_START_TIME + (videoTime * 1000); // Convert seconds to ms
  }, []);

  // Handle video loading
  const handleVideoLoad = useCallback(() => {
    if (videoRef.current) {
      setIsVideoLoaded(true);
      setVideoDuration(videoRef.current.duration);
      setVideoError(null);
      console.log(`📹 Video loaded: duration ${videoRef.current.duration}s`);
    }
  }, []);

  const handleVideoError = useCallback((error: any) => {
    console.error('Video loading error:', error);
    setVideoError('Failed to load video file. Make sure the video file is accessible.');
    setIsVideoLoaded(false);
  }, []);

  const handleVideoTimeUpdate = useCallback(() => {
    if (videoRef.current && !isSyncing) {
      const currentVideoTime = videoRef.current.currentTime;
      setVideoCurrentTime(currentVideoTime);
      
      // Optionally notify parent of time changes from video
      if (onTimeUpdate) {
        const ionTime = convertVideoTimeToIonTime(currentVideoTime);
        onTimeUpdate(ionTime);
      }
    }
  }, [isSyncing, onTimeUpdate, convertVideoTimeToIonTime]);

  const handleVideoPlay = useCallback(() => {
    setIsVideoPlaying(true);
  }, []);

  const handleVideoPause = useCallback(() => {
    setIsVideoPlaying(false);
  }, []);

  // Sync video playback with ION playback time
  useEffect(() => {
    if (!videoRef.current || !isVideoLoaded) return;

    const video = videoRef.current;
    const targetVideoTime = convertIonTimeToVideoTime(currentTime);
    
    // Only sync if there's a significant difference (avoid constant seeking)
    const timeDifference = Math.abs(video.currentTime - targetVideoTime);
    
    if (timeDifference > 0.1) { // 100ms tolerance
      setIsSyncing(true);
      video.currentTime = targetVideoTime;
      console.log(`📹 Syncing video to ${targetVideoTime.toFixed(2)}s (ION time: ${currentTime})`);
      
      // Reset syncing flag after a short delay
      setTimeout(() => setIsSyncing(false), 100);
    }
  }, [currentTime, isVideoLoaded, convertIonTimeToVideoTime]);

  // Sync video play/pause state with ION playback
  useEffect(() => {
    if (!videoRef.current || !isVideoLoaded) return;

    const video = videoRef.current;
    
    if (isPlaying && video.paused) {
      video.play().catch(console.error);
    } else if (!isPlaying && !video.paused) {
      video.pause();
    }
  }, [isPlaying, isVideoLoaded]);

  // Format time for display
  const formatTime = useCallback((timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  return (
    <div className="bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl shadow-xl p-6 border-2 border-slate-200">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-slate-600 rounded-xl flex items-center justify-center">
            <span className="text-2xl">📹</span>
          </div>
          <div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-600 to-gray-600 bg-clip-text text-transparent">
              Camera View
            </h2>
            <p className="text-slate-500 text-sm">
              Synchronized video playback from session recording
            </p>
          </div>
        </div>
        
        {/* Video Status */}
        <div className="flex items-center space-x-2">
          {isVideoLoaded && (
            <>
              <div className={`px-3 py-1 rounded-lg text-xs font-medium ${
                isVideoPlaying ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'
              }`}>
                {isVideoPlaying ? '▶️ Playing' : '⏸️ Paused'}
              </div>
              <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg text-xs font-medium">
                🕒 {formatTime(videoCurrentTime)} / {formatTime(videoDuration)}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Video Container */}
      <div className="relative bg-black rounded-lg overflow-hidden">
        {videoError ? (
          <div className="aspect-video flex items-center justify-center bg-gray-900 text-white">
            <div className="text-center">
              <div className="text-4xl mb-4">⚠️</div>
              <div className="text-lg font-semibold mb-2">Video Loading Error</div>
              <div className="text-sm text-gray-300">{videoError}</div>
              <div className="text-xs text-gray-400 mt-2">
                Expected: /assets/Screencast from 03-04-2025 01_12_16 AM.webm
              </div>
            </div>
          </div>
        ) : (
          <>
            <video
              ref={videoRef}
              src="/assets/Screencast from 03-04-2025 01_12_16 AM.webm"
              className="w-full aspect-video"
              onLoadedData={handleVideoLoad}
              onError={handleVideoError}
              onTimeUpdate={handleVideoTimeUpdate}
              onPlay={handleVideoPlay}
              onPause={handleVideoPause}
              controls={false} // We'll control playback via ION playback controls
              preload="metadata"
            />
            
            {/* Loading Overlay */}
            {!isVideoLoaded && !videoError && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-900 text-white">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                  <div className="text-lg font-semibold">Loading Video...</div>
                  <div className="text-sm text-gray-300">Screencast from 03-04-2025 01_12_16 AM.webm</div>
                </div>
              </div>
            )}

            {/* Timestamp Overlay */}
            {isVideoLoaded && (
              <div className="absolute top-4 left-4 bg-black bg-opacity-75 text-white px-3 py-1 rounded text-sm font-mono">
                ION: {currentTime.toFixed(0)} | Video: {formatTime(videoCurrentTime)}
              </div>
            )}

            {/* Sync Status Overlay */}
            {isSyncing && (
              <div className="absolute top-4 right-4 bg-blue-600 bg-opacity-90 text-white px-3 py-1 rounded text-sm">
                🔄 Syncing...
              </div>
            )}
          </>
        )}
      </div>

      {/* Video Info */}
      {isVideoLoaded && (
        <div className="mt-4 flex justify-between items-center text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <span>📁 Screencast from 03-04-2025 01_12_16 AM.webm</span>
            <span>⏱️ Duration: {formatTime(videoDuration)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span className={isPlaying ? 'text-green-600' : 'text-gray-500'}>
              {isPlaying ? '🎬 Synced with playback' : '⏸️ Paused with playback'}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CameraView;
