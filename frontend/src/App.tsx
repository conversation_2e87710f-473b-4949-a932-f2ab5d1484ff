import { useState, useCallback } from 'react';
import { SessionInfoPanel, type SessionInfo } from './components/SessionInfoPanel';
import { RobotInfoPanel, type RobotInfo } from './components/RobotInfoPanel';
import PlaybackControl from './components/PlaybackControlSimple';
import TopicReader from './components/TopicReaderSimple';

import { PlaybackProvider, usePlayback } from './contexts/PlaybackContext';

/**
 * Interface for ION metadata structure
 */
interface IonMetadata {
  session: SessionInfo;
  robot: RobotInfo;
  rawData?: any;
}

/**
 * Main content component that uses playback context
 */
function AppContent() {
  const {
    currentTime,
    timeRange,
    isPlaying,
    playbackSpeed,
    selectedTopic,
    setSelectedTopic,
    togglePlayPause,
    seekTo,
    setPlaybackSpeed
  } = usePlayback();
  const [metadata, setMetadata] = useState<IonMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Mock data for demonstration (since we don't have backend integration yet)
  const loadSampleData = useCallback(() => {
    setIsLoading(true);
    setError(null);

    // Simulate loading delay
    setTimeout(() => {
      const sampleMetadata: IonMetadata = {
        session: {
          sessionId: 'session_12345',
          timestamp: '2024-01-15T10:30:00Z',
          startTime: '2024-01-15T10:30:00Z',
          endTime: '2024-01-15T11:45:00Z',
          duration: 4500,
          version: '1.2.3'
        },
        robot: {
          robotId: 'robot_abc123',
          robotName: 'Explorer-1',
          model: 'RX-2000',
          firmware: 'v2.1.4',
          serialNumber: 'SN123456789',
          capabilities: ['navigation', 'mapping', 'object_detection', 'voice_control'],
          sensors: ['lidar', 'camera', 'imu', 'ultrasonic', 'gps']
        }
      };

      setMetadata(sampleMetadata);
      setIsLoading(false);
    }, 1000);
  }, []);

  const clearData = useCallback(() => {
    setMetadata(null);
    setError(null);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-white via-blue-50 to-purple-50 shadow-xl border-b border-slate-200/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center space-x-4">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  ION Playback Tool
                </h1>
                <p className="text-slate-600 mt-1 text-lg font-medium">
                  Real-time ROS Topic Streaming & Playback Control
                </p>
                <div className="flex items-center space-x-3 mt-2">
                  <div className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-bold">
                    ✅ Task 2 Complete
                  </div>
                  <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-bold">
                    🚀 Live Streaming
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={loadSampleData}
                disabled={isLoading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? 'Loading...' : 'Load Sample Data'}
              </button>
              <button
                onClick={clearData}
                disabled={isLoading}
                className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Clear Data
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="text-red-600 mr-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-medium">Error</h3>
                <p className="text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Task 2: Playback Controls and Topic Reader */}
        <div className="space-y-6">
          {/* Playback Control */}
          <PlaybackControl
            timeRange={timeRange}
            currentTime={currentTime}
            isPlaying={isPlaying}
            playbackSpeed={playbackSpeed}
            onTimeChange={seekTo}
            onPlayPause={togglePlayPause}
            onSpeedChange={setPlaybackSpeed}
          />

          {/* Topic Reader */}
          <TopicReader
            currentTime={currentTime}
            isPlaying={isPlaying}
            onTopicSelect={setSelectedTopic}
          />
        </div>

        {/* Task 1: Metadata Panels (Optional) */}
        {!metadata && !isLoading && !error && (
          <div className="text-center py-12 mt-8">
            <div className="text-6xl mb-4">📊</div>
            <h2 className="text-xl font-semibold text-gray-700 mb-2">
              Optional: Load Metadata
            </h2>
            <p className="text-gray-500 mb-6">
              Click "Load Sample Data" to also see metadata panels from Task 1.
            </p>
          </div>
        )}

        {(metadata || isLoading) && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Session Information Panel */}
            <SessionInfoPanel
              sessionInfo={metadata?.session || {}}
              isLoading={isLoading}
            />

            {/* Robot Information Panel */}
            <RobotInfoPanel
              robotInfo={metadata?.robot || {}}
              isLoading={isLoading}
            />
          </div>
        )}

        {/* Development Notes */}
        <div className="mt-12 p-6 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-900 mb-3">
            🎉 Development Notes - Task 2 Complete
          </h3>
          <div className="text-green-800 space-y-2">
            <p>✅ <strong>Backend:</strong> Topic reader functions with nearest-timestamp matching</p>
            <p>✅ <strong>API:</strong> Express server with CORS support and topic endpoints</p>
            <p>✅ <strong>Frontend:</strong> PlaybackControl component with play/pause/speed controls</p>
            <p>✅ <strong>Frontend:</strong> TopicReader component with dropdown and live message display</p>
            <p>✅ <strong>State Management:</strong> React Context for shared playback state</p>
            <p>✅ <strong>Real-time Sync:</strong> Components react live to playback time changes</p>
            <p>🎯 <strong>Features:</strong> 31 readable topics, 5 speed options, smooth seeking</p>
          </div>
        </div>
      </main>
    </div>
  );
}

/**
 * Main App component with PlaybackProvider wrapper
 */
function App() {
  return (
    <PlaybackProvider>
      <AppContent />
    </PlaybackProvider>
  );
}

export default App;
