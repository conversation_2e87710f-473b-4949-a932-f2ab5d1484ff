import { useState, useCallback, useEffect } from 'react';
import { SessionInfoPanel, type SessionInfo } from './components/SessionInfoPanel';
import { RobotInfoPanel, type RobotInfo } from './components/RobotInfoPanel';
import PlaybackControl from './components/PlaybackControlSimple';
import TopicReader from './components/TopicReaderSimple';
import LogConsole from './components/LogConsole';
import CameraView from './components/CameraView';

import { PlaybackProvider, usePlayback } from './contexts/PlaybackContext';

/**
 * Interface for ION metadata structure
 */
interface IonMetadata {
  session: SessionInfo;
  robot: RobotInfo;
  rawData?: any;
}

/**
 * Main content component that uses playback context
 */
function AppContent() {
  const {
    currentTime,
    timeRange,
    isPlaying,
    playbackSpeed,
    selectedTopic,
    setSelectedTopic,
    togglePlayPause,
    seekTo,
    setPlaybackSpeed
  } = usePlayback();
  const [metadata, setMetadata] = useState<IonMetadata | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load real metadata from OhmniClean_log.ion via backend API
  const loadMetadata = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('http://localhost:3001/api/metadata');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const metadataFromApi = await response.json();

      const ionMetadata: IonMetadata = {
        session: metadataFromApi.session,
        robot: metadataFromApi.robot,
        rawData: metadataFromApi
      };

      setMetadata(ionMetadata);
      setIsLoading(false);
    } catch (err) {
      console.error('Error loading metadata:', err);
      setError('Failed to load metadata from OhmniClean_log.ion. Make sure the backend server is running.');
      setIsLoading(false);
    }
  }, []);

  // Load metadata automatically when component mounts
  useEffect(() => {
    loadMetadata();
  }, [loadMetadata]);



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50">
      {/* Header */}
      <header className="bg-gradient-to-r from-white via-blue-50 to-purple-50 shadow-xl border-b border-slate-200/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center space-x-4">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-500 via-purple-600 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg">
                <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                  ION Playback Tool
                </h1>
                <p className="text-slate-600 mt-1 text-lg font-medium">
                  Real-time ROS Topic Streaming & Playback Control
                </p>
                <div className="flex items-center space-x-3 mt-2">
                  <div className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-bold">
                    ✅ Task 4 Complete
                  </div>
                  <div className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-bold">
                    🚀 Live Streaming
                  </div>
                  <div className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-bold">
                    📜 Log Console
                  </div>
                  <div className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-bold">
                    📹 Camera View
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-bold">
                🤖 Auto-loaded ION Data
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="text-red-600 mr-3">⚠️</div>
              <div>
                <h3 className="text-red-800 font-medium">Error</h3>
                <p className="text-red-700 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Task 1: Metadata Panels - Auto-loaded from OhmniClean_log.ion */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
              📊 ION Metadata Panels
            </h2>
            <p className="text-slate-600">
              Metadata automatically loaded from <span className="font-mono bg-slate-100 px-2 py-1 rounded">OhmniClean_log.ion</span>
            </p>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Session Information Panel */}
            <SessionInfoPanel
              sessionInfo={metadata?.session || {}}
              isLoading={isLoading}
            />

            {/* Robot Information Panel */}
            <RobotInfoPanel
              robotInfo={metadata?.robot || {}}
              isLoading={isLoading}
            />
          </div>
        </div>

        {/* Task 2: Playback Controls and Topic Reader */}
        <div className="space-y-6">
          {/* Playback Control */}
          <PlaybackControl
            timeRange={timeRange}
            currentTime={currentTime}
            isPlaying={isPlaying}
            playbackSpeed={playbackSpeed}
            onTimeChange={seekTo}
            onPlayPause={togglePlayPause}
            onSpeedChange={setPlaybackSpeed}
            selectedTopic={selectedTopic}
          />

          {/* Topic Reader */}
          <TopicReader
            currentTime={currentTime}
            isPlaying={isPlaying}
            onTopicSelect={setSelectedTopic}
          />
        </div>

        {/* Task 3: Log Console */}
        <div className="mt-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-600 to-gray-600 bg-clip-text text-transparent mb-2">
              📜 ROS Log Console
            </h2>
            <p className="text-slate-600">
              Live console logs from <span className="font-mono bg-slate-100 px-2 py-1 rounded">/rosout_agg</span> topic
            </p>
          </div>
          <LogConsole
            currentTime={currentTime}
            isPlaying={isPlaying}
          />
        </div>

        {/* Task 4: Camera View */}
        <div className="mt-8">
          <div className="text-center mb-6">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-slate-600 to-gray-600 bg-clip-text text-transparent mb-2">
              📹 Camera View
            </h2>
            <p className="text-slate-600">
              Synchronized video playback from <span className="font-mono bg-slate-100 px-2 py-1 rounded">session recording</span>
            </p>
          </div>
          <CameraView
            currentTime={currentTime}
            isPlaying={isPlaying}
          />
        </div>

        {/* Development Notes */}
        <div className="mt-12 p-6 bg-green-50 border border-green-200 rounded-lg">
          <h3 className="text-lg font-semibold text-green-900 mb-3">
            🎉 Development Notes - Task 4 Complete
          </h3>
          <div className="text-green-800 space-y-2">
            <p>✅ <strong>Task 1:</strong> ION Metadata Panels with Session & Robot information</p>
            <p>✅ <strong>Task 2:</strong> Playback Controls & Topic Reader with real-time streaming</p>
            <p>✅ <strong>Task 3:</strong> Log Console from /rosout_agg with search & filtering</p>
            <p>✅ <strong>Task 4:</strong> Camera View with synchronized video playback</p>
            <p>✅ <strong>Backend:</strong> Static file serving for video assets via /assets endpoint</p>
            <p>✅ <strong>Frontend:</strong> CameraView component with HTML5 video and time synchronization</p>
            <p>✅ <strong>Features:</strong> Auto-sync with playback time, play/pause sync, timestamp overlay</p>
            <p>🎯 <strong>Complete:</strong> Full ION playback tool with metadata, streaming, logging, and video</p>
          </div>
        </div>
      </main>
    </div>
  );
}

/**
 * Main App component with PlaybackProvider wrapper
 */
function App() {
  return (
    <PlaybackProvider>
      <AppContent />
    </PlaybackProvider>
  );
}

export default App;
