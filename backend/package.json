{"name": "ion-playback-backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "server": "ts-node src/server.ts", "server:watch": "nodemon --exec ts-node src/server.ts", "watch": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ion", "parser", "amazon-ion", "typescript"], "author": "", "license": "ISC", "description": "Backend for ION log playback tool - parses Amazon ION files and extracts metadata", "dependencies": {"@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/node": "^22.15.24", "cors": "^2.8.5", "express": "^5.1.0", "ion-js": "^5.2.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"nodemon": "^3.1.10"}}