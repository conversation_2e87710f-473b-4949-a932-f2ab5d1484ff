const express = require('express');
const cors = require('cors');
const path = require('path');

// Import the compiled JavaScript versions of our functions
// We'll need to compile TypeScript first
const app = express();
const PORT = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// ION file path
const ION_FILE_PATH = path.join(__dirname, '../../OhmniClean_log.ion');

// For now, let's create a simple mock server to test the frontend
// We'll replace this with actual functions once TypeScript compilation works

// Mock data based on our test results
const mockTopics = [
  {
    name: '/tf',
    type: 'tf2_msgs/TFMessage',
    frequency: 62.55,
    messageCount: 8412,
    firstTimestamp: 1740105930833.6643,
    lastTimestamp: 1740106071391.3274
  },
  {
    name: '/tb_control/wheel_odom',
    type: 'nav_msgs/Odometry',
    frequency: 61.93,
    messageCount: 6988,
    firstTimestamp: 1740105930280,
    lastTimestamp: 1740106071390
  },
  {
    name: '/tb_cmd_vel',
    type: 'geometry_msgs/Twist',
    frequency: 9.99,
    messageCount: 1294,
    firstTimestamp: 1740105941261.7908,
    lastTimestamp: 1740106070764.142
  },
  {
    name: '/tb_node/yaw',
    type: 'std_msgs/Float32',
    frequency: 61.80,
    messageCount: 6839,
    firstTimestamp: 1740105930834.2332,
    lastTimestamp: 1740106071391.3462
  }
];

const mockTimeRange = {
  start: 1740105930280,
  end: 1740106071391.3462,
  duration: 141111.0662
};

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Get readable topics
app.get('/api/topics/readable', (req, res) => {
  console.log('📡 GET /api/topics/readable');
  res.json(mockTopics);
});

// Get playback time range
app.get('/api/playback/timerange', (req, res) => {
  console.log('⏱️  GET /api/playback/timerange');
  res.json(mockTimeRange);
});

// Get message at timestamp
app.get('/api/topics/:topicName/message', (req, res) => {
  const topicName = decodeURIComponent(req.params.topicName);
  const timestamp = parseFloat(req.query.timestamp);

  console.log(`📨 GET /api/topics/${topicName}/message?timestamp=${timestamp}`);

  if (isNaN(timestamp)) {
    return res.status(400).json({ error: 'Invalid timestamp' });
  }

  // Find the topic
  const topic = mockTopics.find(t => t.name === topicName);
  if (!topic) {
    return res.status(404).json({ error: 'Topic not found' });
  }

  // Generate mock message based on topic type
  let mockData;

  if (topicName === '/tf') {
    mockData = {
      transforms: [{
        header: { seq: Math.floor(Math.random() * 1000), stamp: timestamp / 1000, frame_id: "odom" },
        child_frame_id: "footprint",
        transform: {
          translation: { x: Math.random() * 5, y: Math.random() * 5, z: 0 },
          rotation: { x: 0, y: 0, z: Math.random() * 0.1, w: 0.99 }
        }
      }]
    };
  } else if (topicName === '/tb_control/wheel_odom') {
    mockData = {
      header: { seq: Math.floor(Math.random() * 1000), stamp: timestamp / 1000, frame_id: "odom" },
      child_frame_id: "footprint",
      pose: {
        pose: {
          position: { x: Math.random() * 5, y: Math.random() * 5, z: 0 },
          orientation: { x: 0, y: 0, z: Math.random() * 0.1, w: 0.99 }
        }
      },
      twist: {
        twist: {
          linear: { x: Math.random() * 0.5, y: 0, z: 0 },
          angular: { x: 0, y: 0, z: Math.random() * 0.2 }
        }
      }
    };
  } else if (topicName === '/tb_cmd_vel') {
    mockData = {
      linear: { x: Math.random() * 0.5, y: 0, z: 0 },
      angular: { x: 0, y: 0, z: Math.random() * 0.2 }
    };
  } else if (topicName === '/tb_node/yaw') {
    mockData = {
      data: Math.random() * Math.PI * 2
    };
  } else {
    mockData = { message: "Mock data for " + topicName };
  }

  const message = {
    timestamp: timestamp,
    data: mockData,
    sequenceNumber: Math.floor(Math.random() * 1000)
  };

  res.json(message);
});

// Get ION metadata
app.get('/api/metadata', (req, res) => {
  console.log('📊 GET /api/metadata');

  try {
    // Mock metadata based on OhmniClean_log.ion analysis
    const metadata = {
      session: {
        sessionId: 'ohmni_clean_session_001',
        timestamp: '2024-01-15T14:30:00Z',
        startTime: '2024-01-15T14:30:00Z',
        endTime: '2024-01-15T14:32:21Z',
        duration: 141000, // 141 seconds in milliseconds
        version: '1.0.0',
        sessionCode: 'CLEAN_001',
        operatorId: 'operator_123',
        operatorName: 'John Doe',
        mapId: 'map_office_floor1',
        mapName: 'Office Floor 1',
        result: 'SUCCESS',
        sessionType: 'cleaning'
      },
      robot: {
        robotId: 'ohmni_robot_001',
        robotName: 'OhmniClean-Alpha',
        model: 'OhmniClean-Pro',
        firmware: 'v3.2.1',
        serialNumber: 'OC-2024-001',
        capabilities: ['autonomous_navigation', 'cleaning', 'mapping', 'obstacle_avoidance', 'voice_control'],
        sensors: ['lidar', 'camera', 'imu', 'ultrasonic', 'wheel_encoders', 'cliff_sensors'],
        enterpriseName: 'Ohmnilabs Inc.',
        enterpriseID: 'ohmnilabs_001',
        siteID: 'site_office_001',
        mapID: 'map_office_floor1',
        mapVersionID: 'v1.2.3',
        releaseTrack: 'stable',
        apkVersion: '2.1.4',
        server: 'ohmni-cloud.com',
        autonomyServer: 'autonomy.ohmni-cloud.com'
      }
    };

    res.json(metadata);
  } catch (error) {
    console.error('Error getting metadata:', error);
    res.status(500).json({ error: 'Failed to load metadata' });
  }
});

// Import the ION parser to access real ION data
const { parseIonMetadata } = require('../dist/ionParser');

// Cache for real log data from /rosout_agg topic
let cachedLogs = null;

async function loadLogsFromIon() {
  if (cachedLogs) {
    return cachedLogs;
  }

  try {
    console.log('📜 Loading real log messages from /rosout_agg topic in ION file...');

    // Parse the ION file to get all data
    const metadata = await parseIonMetadata(ION_FILE_PATH);

    if (!metadata.rawData?.topics || !Array.isArray(metadata.rawData.topics)) {
      console.warn('No topics found in ION file');
      return [];
    }

    // Find the /rosout_agg topic
    const rosoutTopic = metadata.rawData.topics.find(topic => topic.topicName === '/rosout_agg');

    if (!rosoutTopic || !rosoutTopic.messages || !Array.isArray(rosoutTopic.messages)) {
      console.warn('No /rosout_agg topic or messages found in ION file');
      return [];
    }

    // Convert ION messages to our log format
    cachedLogs = rosoutTopic.messages.map(message => ({
      timestamp: message.timestamp,
      level: message.data?.level || 1,
      name: message.data?.name || 'unknown',
      msg: message.data?.msg || '',
      file: message.data?.file || '',
      function: message.data?.function || '',
      line: message.data?.line || 0
    }));

    console.log(`📜 Successfully loaded ${cachedLogs.length} real log messages from /rosout_agg topic`);
    return cachedLogs;

  } catch (error) {
    console.error('Error loading logs from ION file:', error);
    console.log('📜 Falling back to empty logs array');
    return [];
  }
}

// Get logs up to timestamp with optional search filter
app.get('/api/logs', async (req, res) => {
  const timestamp = parseFloat(req.query.timestamp);
  const search = req.query.search || '';

  console.log(`📜 GET /api/logs?timestamp=${timestamp}&search=${search}`);

  if (isNaN(timestamp)) {
    return res.status(400).json({ error: 'Invalid timestamp parameter' });
  }

  try {
    // Load real logs from ION file
    const allLogs = await loadLogsFromIon();

    // Filter logs up to the given timestamp
    let filteredLogs = allLogs.filter(log => log.timestamp <= timestamp);

    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      filteredLogs = filteredLogs.filter(log =>
        log.msg.toLowerCase().includes(searchLower) ||
        log.name.toLowerCase().includes(searchLower)
      );
    }

    // Sort by timestamp (ascending)
    filteredLogs.sort((a, b) => a.timestamp - b.timestamp);

    res.json(filteredLogs);
  } catch (error) {
    console.error('Error getting logs:', error);
    res.status(500).json({ error: 'Failed to get logs' });
  }
});

// Error handling
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ION Playback Server (Mock) running on http://localhost:${PORT}`);
  console.log(`📁 Mock data based on: ${ION_FILE_PATH}`);
  console.log(`🌐 API endpoints:`);
  console.log(`   GET /health`);
  console.log(`   GET /api/topics/readable`);
  console.log(`   GET /api/playback/timerange`);
  console.log(`   GET /api/topics/:topicName/message?timestamp=123`);
  console.log(`   GET /api/metadata`);
  console.log(`   GET /api/logs?timestamp=123&search=optional`);
  console.log(`\n🔧 Note: This is a mock server for frontend development.`);
  console.log(`   Replace with TypeScript version once compilation issues are resolved.`);
});

module.exports = app;
