import { parseIonMetadata } from './ionParser';

/**
 * Interface for readable topic information
 */
export interface ReadableTopic {
  name: string;
  type: string;
  frequency: number;
  messageCount: number;
  firstTimestamp: number;
  lastTimestamp: number;
}

/**
 * Interface for topic message
 */
export interface TopicMessage {
  timestamp: number;
  data: any;
  sequenceNumber?: number;
}

/**
 * Interface for playback time range
 */
export interface PlaybackTimeRange {
  start: number;
  end: number;
  duration: number;
}

/**
 * Cache for parsed ION data to avoid re-parsing
 */
let cachedIonData: any = null;
let cachedFilePath: string = '';

/**
 * Load and cache ION data
 */
async function loadIonData(filePath: string): Promise<any> {
  if (cachedFilePath !== filePath || !cachedIonData) {
    console.log(`Loading ION data from: ${filePath}`);
    const metadata = await parseIonMetadata(filePath);
    cachedIonData = metadata.rawData;
    cachedFilePath = filePath;
  }
  return cachedIonData;
}

/**
 * Check if a topic message is human-readable (not compressed/binary)
 */
function isReadableMessage(message: any): boolean {
  if (!message || !message.data) {
    return false;
  }

  // Skip messages that are likely compressed or binary
  const data = message.data;

  // Check for common binary/compressed indicators
  if (typeof data === 'string' && data.length > 1000) {
    // Very long strings might be base64 encoded binary data
    return false;
  }

  // Check if data contains mostly readable content
  if (typeof data === 'object') {
    // Objects are generally readable
    return true;
  }

  if (typeof data === 'string') {
    // Check if string contains mostly printable characters
    const printableRatio = (data.match(/[\x20-\x7E]/g) || []).length / data.length;
    return printableRatio > 0.8;
  }

  // Numbers, booleans are readable
  if (typeof data === 'number' || typeof data === 'boolean') {
    return true;
  }

  return false;
}

/**
 * Get list of readable topics from ION file
 */
export async function getReadableTopics(filePath: string): Promise<ReadableTopic[]> {
  const ionData = await loadIonData(filePath);

  if (!ionData.topics || !Array.isArray(ionData.topics)) {
    return [];
  }

  const readableTopics: ReadableTopic[] = [];

  for (const topic of ionData.topics) {
    if (!topic.topicName || !topic.messages || !Array.isArray(topic.messages)) {
      continue;
    }

    // Check if topic has readable messages
    const readableMessages = topic.messages.filter(isReadableMessage);

    if (readableMessages.length === 0) {
      continue;
    }

    // Get timestamps for time range
    const timestamps = readableMessages
      .map((msg: any) => msg.timestamp)
      .filter((ts: any) => typeof ts === 'number')
      .sort((a: number, b: number) => a - b);

    if (timestamps.length === 0) {
      continue;
    }

    readableTopics.push({
      name: topic.topicName,
      type: topic.topicType || 'unknown',
      frequency: topic.frequency || 0,
      messageCount: readableMessages.length,
      firstTimestamp: timestamps[0],
      lastTimestamp: timestamps[timestamps.length - 1]
    });
  }

  // Sort by message count (most active topics first)
  return readableTopics.sort((a, b) => b.messageCount - a.messageCount);
}

/**
 * Get message at specific timestamp using nearest-timestamp matching
 */
export async function getMessageAtTimestamp(
  filePath: string,
  topicName: string,
  timestamp: number
): Promise<TopicMessage | null> {
  const ionData = await loadIonData(filePath);

  if (!ionData.topics || !Array.isArray(ionData.topics)) {
    return null;
  }

  // Find the topic
  const topic = ionData.topics.find((t: any) => t.topicName === topicName);
  if (!topic || !topic.messages || !Array.isArray(topic.messages)) {
    return null;
  }

  // Filter readable messages and sort by timestamp
  const readableMessages = topic.messages
    .filter(isReadableMessage)
    .filter((msg: any) => typeof msg.timestamp === 'number')
    .sort((a: any, b: any) => a.timestamp - b.timestamp);

  if (readableMessages.length === 0) {
    return null;
  }

  // Find nearest timestamp using binary search for efficiency
  let left = 0;
  let right = readableMessages.length - 1;
  let bestMatch = readableMessages[0];
  let minDiff = Math.abs(readableMessages[0].timestamp - timestamp);

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    const message = readableMessages[mid];
    const diff = Math.abs(message.timestamp - timestamp);

    if (diff < minDiff) {
      minDiff = diff;
      bestMatch = message;
    }

    if (message.timestamp < timestamp) {
      left = mid + 1;
    } else if (message.timestamp > timestamp) {
      right = mid - 1;
    } else {
      // Exact match
      bestMatch = message;
      break;
    }
  }

  return {
    timestamp: bestMatch.timestamp,
    data: bestMatch.data,
    sequenceNumber: bestMatch.data?.header?.seq
  };
}

/**
 * Get playback time range for the entire session
 */
export async function getPlaybackTimeRange(filePath: string): Promise<PlaybackTimeRange> {
  const ionData = await loadIonData(filePath);

  let minTimestamp = Infinity;
  let maxTimestamp = -Infinity;

  if (ionData.topics && Array.isArray(ionData.topics)) {
    for (const topic of ionData.topics) {
      if (!topic.messages || !Array.isArray(topic.messages)) {
        continue;
      }

      for (const message of topic.messages) {
        if (typeof message.timestamp === 'number') {
          minTimestamp = Math.min(minTimestamp, message.timestamp);
          maxTimestamp = Math.max(maxTimestamp, message.timestamp);
        }
      }
    }
  }

  // Fallback to session info if no timestamps found in topics
  if (minTimestamp === Infinity || maxTimestamp === -Infinity) {
    // Try to get from session metadata
    const metadata = await parseIonMetadata(filePath);
    if (metadata.session.startTime && metadata.session.endTime) {
      const startTime = new Date(metadata.session.startTime).getTime();
      const endTime = new Date(metadata.session.endTime).getTime();
      return {
        start: startTime,
        end: endTime,
        duration: endTime - startTime
      };
    }

    // Default fallback
    return {
      start: 0,
      end: 1000,
      duration: 1000
    };
  }

  return {
    start: minTimestamp,
    end: maxTimestamp,
    duration: maxTimestamp - minTimestamp
  };
}

/**
 * Get all messages for a topic within a time range
 */
export async function getTopicMessages(
  filePath: string,
  topicName: string,
  startTime?: number,
  endTime?: number
): Promise<TopicMessage[]> {
  const ionData = await loadIonData(filePath);

  if (!ionData.topics || !Array.isArray(ionData.topics)) {
    return [];
  }

  const topic = ionData.topics.find((t: any) => t.topicName === topicName);
  if (!topic || !topic.messages || !Array.isArray(topic.messages)) {
    return [];
  }

  let messages = topic.messages
    .filter(isReadableMessage)
    .filter((msg: any) => typeof msg.timestamp === 'number')
    .map((msg: any) => ({
      timestamp: msg.timestamp,
      data: msg.data,
      sequenceNumber: msg.data?.header?.seq
    }))
    .sort((a: any, b: any) => a.timestamp - b.timestamp);

  // Filter by time range if specified
  if (startTime !== undefined) {
    messages = messages.filter((msg: any) => msg.timestamp >= startTime);
  }
  if (endTime !== undefined) {
    messages = messages.filter((msg: any) => msg.timestamp <= endTime);
  }

  return messages;
}
