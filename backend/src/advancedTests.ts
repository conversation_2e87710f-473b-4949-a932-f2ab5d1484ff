import { parseIonMetadata } from './ionParser';
import * as path from 'path';

/**
 * Advanced testing utilities for ION file analysis
 */

interface TopicInfo {
  topicName: string;
  topicType: string;
  frequency: number;
  messageCount: number;
  firstTimestamp?: number;
  lastTimestamp?: number;
  sampleMessage?: any;
}

interface PerformanceMetrics {
  fileSize: number;
  parseTime: number;
  memoryUsage: {
    before: NodeJS.MemoryUsage;
    after: NodeJS.MemoryUsage;
    delta: NodeJS.MemoryUsage;
  };
}

/**
 * Analyzes topics in the ION file
 */
function analyzeTopics(rawData: any): TopicInfo[] {
  const topics: TopicInfo[] = [];

  if (!rawData.topics || !Array.isArray(rawData.topics)) {
    return topics;
  }

  for (const topic of rawData.topics) {
    if (!topic.topicName || !topic.messages) continue;

    const messages = Array.isArray(topic.messages) ? topic.messages : [];
    const timestamps = messages
      .map((msg: any) => msg.timestamp)
      .filter((ts: any) => typeof ts === 'number')
      .sort((a: number, b: number) => a - b);

    const topicInfo: TopicInfo = {
      topicName: topic.topicName,
      topicType: topic.topicType || 'unknown',
      frequency: topic.frequency || 0,
      messageCount: messages.length,
      firstTimestamp: timestamps[0],
      lastTimestamp: timestamps[timestamps.length - 1],
      sampleMessage: messages[0] // First message as sample
    };

    topics.push(topicInfo);
  }

  return topics.sort((a, b) => b.messageCount - a.messageCount); // Sort by message count
}

/**
 * Analyzes map data if present
 */
function analyzeMapData(rawData: any) {
  const mapInfo: any = {};

  if (rawData.metadata?.mapMetadata) {
    const meta = rawData.metadata.mapMetadata;
    mapInfo.metadata = {
      loadTime: meta.map_load_time,
      resolution: meta.resolution,
      width: meta.width,
      height: meta.height,
      origin: meta.origin
    };
  }

  if (rawData.metadata?.mapData) {
    const mapData = rawData.metadata.mapData;
    mapInfo.data = {
      format: mapData.format,
      dataSize: mapData.data ? mapData.data.length : 0,
      dataType: typeof mapData.data
    };
  }

  return mapInfo;
}

/**
 * Performance test for parsing
 */
async function performanceTest(filePath: string): Promise<PerformanceMetrics> {
  const fs = require('fs');
  const fileSize = fs.statSync(filePath).size;

  // Measure memory before
  const memoryBefore = process.memoryUsage();

  // Measure parse time
  const startTime = Date.now();
  await parseIonMetadata(filePath);
  const parseTime = Date.now() - startTime;

  // Measure memory after
  const memoryAfter = process.memoryUsage();

  // Calculate delta
  const memoryDelta: NodeJS.MemoryUsage = {
    rss: memoryAfter.rss - memoryBefore.rss,
    heapTotal: memoryAfter.heapTotal - memoryBefore.heapTotal,
    heapUsed: memoryAfter.heapUsed - memoryBefore.heapUsed,
    external: memoryAfter.external - memoryBefore.external,
    arrayBuffers: memoryAfter.arrayBuffers - memoryBefore.arrayBuffers
  };

  return {
    fileSize,
    parseTime,
    memoryUsage: {
      before: memoryBefore,
      after: memoryAfter,
      delta: memoryDelta
    }
  };
}

/**
 * Comprehensive analysis of the ION file
 */
async function comprehensiveAnalysis() {
  console.log('🔬 Comprehensive ION File Analysis');
  console.log('==================================\n');

  const filePath = path.join(__dirname, '../../OhmniClean_log.ion');

  try {
    // Performance test
    console.log('⏱️  Performance Analysis');
    console.log('------------------------');
    const perfMetrics = await performanceTest(filePath);
    console.log(`File size: ${(perfMetrics.fileSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Parse time: ${perfMetrics.parseTime} ms`);
    console.log(`Memory used: ${(perfMetrics.memoryUsage.delta.heapUsed / 1024 / 1024).toFixed(2)} MB`);
    console.log();

    // Parse the file
    const metadata = await parseIonMetadata(filePath);

    // Topic analysis
    console.log('📡 Topic Analysis');
    console.log('-----------------');
    const topics = analyzeTopics(metadata.rawData);
    console.log(`Total topics: ${topics.length}`);

    if (topics.length > 0) {
      console.log('\nTop topics by message count:');
      topics.slice(0, 10).forEach((topic, index) => {
        const duration = topic.lastTimestamp && topic.firstTimestamp
          ? ((topic.lastTimestamp - topic.firstTimestamp) / 1000).toFixed(1)
          : 'unknown';
        console.log(`${index + 1}. ${topic.topicName}`);
        console.log(`   Type: ${topic.topicType}`);
        console.log(`   Messages: ${topic.messageCount}`);
        console.log(`   Frequency: ${topic.frequency} Hz`);
        console.log(`   Duration: ${duration}s`);
        console.log();
      });
    }

    // Map analysis
    console.log('🗺️  Map Data Analysis');
    console.log('---------------------');
    const mapInfo = analyzeMapData(metadata.rawData);
    if (mapInfo.metadata) {
      console.log('Map metadata:');
      console.log(`  Resolution: ${mapInfo.metadata.resolution} m/pixel`);
      console.log(`  Dimensions: ${mapInfo.metadata.width} x ${mapInfo.metadata.height} pixels`);
      console.log(`  Load time: ${new Date(mapInfo.metadata.loadTime * 1000).toISOString()}`);
    }
    if (mapInfo.data) {
      console.log('Map data:');
      console.log(`  Format: ${mapInfo.data.format}`);
      console.log(`  Data size: ${(mapInfo.data.dataSize / 1024).toFixed(2)} KB`);
    }
    console.log();

    // Session summary
    console.log('📊 Session Summary');
    console.log('------------------');
    const session = metadata.session;
    console.log(`Robot: ${metadata.robot.robotName} (${metadata.robot.robotId?.substring(0, 8)}...)`);
    console.log(`Session: ${session.sessionCode} (${session.sessionType})`);
    console.log(`Duration: ${session.duration ? (session.duration / 1000).toFixed(1) : 'unknown'}s`);
    console.log(`Path length: ${session.total_path_length?.toFixed(2) || 'unknown'}m`);
    console.log(`Result: ${session.result} (${(session.completeness * 100).toFixed(1)}% complete)`);
    console.log(`Operator: ${session.operatorName || 'Unknown'}`);
    console.log(`Map: ${session.mapName} (${session.mapId?.substring(0, 8)}...)`);

  } catch (error) {
    console.error('Error in comprehensive analysis:', error);
  }
}

/**
 * Export data to JSON for further analysis
 */
async function exportToJson() {
  console.log('💾 Exporting parsed data to JSON');
  console.log('=================================\n');

  const filePath = path.join(__dirname, '../../OhmniClean_log.ion');
  const outputPath = path.join(__dirname, '../../OhmniClean_parsed.json');

  try {
    const metadata = await parseIonMetadata(filePath);

    // Create a summary object for export
    const exportData = {
      metadata: {
        session: metadata.session,
        robot: metadata.robot
      },
      topics: analyzeTopics(metadata.rawData),
      mapInfo: analyzeMapData(metadata.rawData),
      exportTimestamp: new Date().toISOString(),
      sourceFile: 'OhmniClean_log.ion'
    };

    const fs = require('fs');
    fs.writeFileSync(outputPath, JSON.stringify(exportData, null, 2));

    console.log(`✅ Data exported to: ${outputPath}`);
    console.log(`📊 Export contains:`);
    console.log(`   - Session metadata`);
    console.log(`   - Robot information`);
    console.log(`   - ${exportData.topics.length} topic summaries`);
    console.log(`   - Map information`);

  } catch (error) {
    console.error('Error exporting data:', error);
  }
}

// Run comprehensive analysis if this file is executed directly
if (require.main === module) {
  (async () => {
    await comprehensiveAnalysis();
    console.log('\n' + '='.repeat(50) + '\n');
    await exportToJson();
  })().catch(console.error);
}

export { comprehensiveAnalysis, exportToJson, analyzeTopics, analyzeMapData, performanceTest };
