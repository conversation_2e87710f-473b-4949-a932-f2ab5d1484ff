import { parseIonMetadata, isValidIonFile } from './ionParser';
import * as ion from 'ion-js';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Comprehensive test script for the OhmniClean_log.ion file
 * This script will analyze the structure and content of the ION file
 */

function analyzeStructure(obj: any, depth: number = 0, maxDepth: number = 3): string {
  const indent = '  '.repeat(depth);
  
  if (depth > maxDepth) {
    return `${indent}[Max depth reached]`;
  }
  
  if (obj === null || obj === undefined) {
    return `${indent}${obj}`;
  }
  
  if (typeof obj === 'string') {
    const preview = obj.length > 50 ? obj.substring(0, 50) + '...' : obj;
    return `${indent}"${preview}" (string, length: ${obj.length})`;
  }
  
  if (typeof obj === 'number') {
    return `${indent}${obj} (number)`;
  }
  
  if (typeof obj === 'boolean') {
    return `${indent}${obj} (boolean)`;
  }
  
  if (Array.isArray(obj)) {
    let result = `${indent}Array (length: ${obj.length})`;
    if (obj.length > 0 && depth < maxDepth) {
      result += '\n' + `${indent}[0]: ` + analyzeStructure(obj[0], depth + 1, maxDepth).trim();
      if (obj.length > 1) {
        result += '\n' + `${indent}[1]: ` + analyzeStructure(obj[1], depth + 1, maxDepth).trim();
      }
      if (obj.length > 2) {
        result += `\n${indent}... (${obj.length - 2} more items)`;
      }
    }
    return result;
  }
  
  if (typeof obj === 'object') {
    const keys = Object.keys(obj);
    let result = `${indent}Object (${keys.length} keys)`;
    
    if (keys.length > 0 && depth < maxDepth) {
      // Show first few keys
      const keysToShow = keys.slice(0, 5);
      for (const key of keysToShow) {
        result += `\n${indent}${key}: ` + analyzeStructure(obj[key], depth + 1, maxDepth).trim();
      }
      if (keys.length > 5) {
        result += `\n${indent}... (${keys.length - 5} more keys: ${keys.slice(5, 10).join(', ')}${keys.length > 10 ? '...' : ''})`;
      }
    }
    return result;
  }
  
  return `${indent}${typeof obj}: ${String(obj)}`;
}

function findSessionData(obj: any, path: string = 'root'): string[] {
  const results: string[] = [];
  
  if (obj === null || obj === undefined) {
    return results;
  }
  
  // Look for session-related keys
  if (typeof obj === 'object' && !Array.isArray(obj)) {
    const keys = Object.keys(obj);
    
    // Check for session-related keys
    const sessionKeys = keys.filter(key => 
      key.toLowerCase().includes('session') ||
      key.toLowerCase().includes('time') ||
      key.toLowerCase().includes('duration') ||
      key.toLowerCase().includes('start') ||
      key.toLowerCase().includes('end') ||
      key.toLowerCase().includes('id')
    );
    
    if (sessionKeys.length > 0) {
      results.push(`Found session-related keys at ${path}: ${sessionKeys.join(', ')}`);
      
      // Show values for these keys
      for (const key of sessionKeys.slice(0, 3)) { // Limit to first 3 to avoid spam
        const value = obj[key];
        if (typeof value === 'string' || typeof value === 'number') {
          results.push(`  ${key}: ${value}`);
        } else if (typeof value === 'object') {
          results.push(`  ${key}: [object with ${Object.keys(value || {}).length} keys]`);
        }
      }
    }
    
    // Recursively search in nested objects (limit depth)
    if (path.split('.').length < 4) {
      for (const key of keys.slice(0, 10)) { // Limit to first 10 keys to avoid performance issues
        if (typeof obj[key] === 'object') {
          results.push(...findSessionData(obj[key], `${path}.${key}`));
        }
      }
    }
  }
  
  if (Array.isArray(obj) && path.split('.').length < 4) {
    // Check first few array elements
    for (let i = 0; i < Math.min(3, obj.length); i++) {
      results.push(...findSessionData(obj[i], `${path}[${i}]`));
    }
  }
  
  return results;
}

async function testOhmniCleanFile() {
  console.log('🧪 Comprehensive Test of OhmniClean_log.ion');
  console.log('===========================================\n');
  
  const filePath = path.join(__dirname, '../../OhmniClean_log.ion');
  
  // Test 1: File validation
  console.log('📋 Test 1: File Validation');
  console.log('---------------------------');
  console.log(`File path: ${filePath}`);
  console.log(`File exists: ${fs.existsSync(filePath)}`);
  
  if (fs.existsSync(filePath)) {
    const stats = fs.statSync(filePath);
    console.log(`File size: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Modified: ${stats.mtime.toISOString()}`);
  }
  
  console.log(`Valid ION file: ${isValidIonFile(filePath)}`);
  console.log();
  
  // Test 2: Raw ION parsing
  console.log('📋 Test 2: Raw ION Structure Analysis');
  console.log('-------------------------------------');
  
  try {
    const ionData = fs.readFileSync(filePath);
    const domValues = ion.dom.loadAll(ionData);
    
    console.log(`Number of top-level ION values: ${domValues.length}`);
    
    // Analyze first few values
    for (let i = 0; i < Math.min(3, domValues.length); i++) {
      console.log(`\nValue ${i + 1}:`);
      console.log(`  Type: ${domValues[i].getType()}`);
      console.log(`  String representation: ${domValues[i].toString().substring(0, 200)}...`);
    }
    
  } catch (error) {
    console.error('Error in raw ION parsing:', error);
  }
  
  console.log();
  
  // Test 3: Metadata extraction with current parser
  console.log('📋 Test 3: Current Parser Results');
  console.log('---------------------------------');
  
  try {
    const metadata = await parseIonMetadata(filePath);
    console.log('Session info keys:', Object.keys(metadata.session));
    console.log('Robot info keys:', Object.keys(metadata.robot));
    console.log('Raw data type:', typeof metadata.rawData);
    console.log('Raw data is array:', Array.isArray(metadata.rawData));
    
    if (Array.isArray(metadata.rawData)) {
      console.log('Raw data array length:', metadata.rawData.length);
    } else if (typeof metadata.rawData === 'object' && metadata.rawData !== null) {
      console.log('Raw data object keys:', Object.keys(metadata.rawData).slice(0, 10));
    }
    
  } catch (error) {
    console.error('Error in metadata extraction:', error);
  }
  
  console.log();
  
  // Test 4: Structure analysis
  console.log('📋 Test 4: Data Structure Analysis');
  console.log('----------------------------------');
  
  try {
    const metadata = await parseIonMetadata(filePath);
    console.log('Structure analysis:');
    console.log(analyzeStructure(metadata.rawData, 0, 2));
    
  } catch (error) {
    console.error('Error in structure analysis:', error);
  }
  
  console.log();
  
  // Test 5: Search for session data
  console.log('📋 Test 5: Session Data Discovery');
  console.log('---------------------------------');
  
  try {
    const metadata = await parseIonMetadata(filePath);
    const sessionFindings = findSessionData(metadata.rawData);
    
    if (sessionFindings.length > 0) {
      console.log('Found potential session data:');
      sessionFindings.slice(0, 20).forEach(finding => console.log(finding)); // Limit output
      if (sessionFindings.length > 20) {
        console.log(`... and ${sessionFindings.length - 20} more findings`);
      }
    } else {
      console.log('No obvious session data found with current search criteria');
    }
    
  } catch (error) {
    console.error('Error in session data discovery:', error);
  }
  
  console.log('\n✅ Test completed!');
}

// Run the test
if (require.main === module) {
  testOhmniCleanFile().catch(console.error);
}

export { testOhmniCleanFile };
