import { parseIonMetadata, isValidIonFile } from './ionParser';
import * as path from 'path';

/**
 * Example usage of the ION parser
 * This demonstrates how to use the parseIonMetadata function
 */
async function main() {
  console.log('ION Playback Backend - Task 1: Parse ION and Display Metadata');
  console.log('================================================================');

  // Test with the OhmniClean_log.ion file
  const exampleFilePath = path.join(__dirname, '../../OhmniClean_log.ion');

  try {
    // Check if file exists and is valid ION
    if (isValidIonFile(exampleFilePath)) {
      console.log(`Parsing ION file: ${exampleFilePath}`);

      const metadata = await parseIonMetadata(exampleFilePath);

      console.log('\n📊 Extracted Metadata:');
      console.log('======================');

      console.log('\n🔧 Session Information:');
      console.log(JSON.stringify(metadata.session, null, 2));

      console.log('\n🤖 Robot Information:');
      console.log(JSON.stringify(metadata.robot, null, 2));

    } else {
      console.log(`Sample ION file not found at: ${exampleFilePath}`);
      console.log('To test the parser, place a binary ION file at the above path.');
    }

  } catch (error) {
    console.error('Error parsing ION file:', error);
  }
}

// Export the parser functions for use by other modules
export { parseIonMetadata, isValidIonFile } from './ionParser';

// Run main function if this file is executed directly
if (require.main === module) {
  main().catch(console.error);
}
