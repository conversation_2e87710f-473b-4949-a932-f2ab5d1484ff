import { 
  getReadableTopics, 
  getMessageAtTimestamp, 
  getPlaybackTimeRange,
  getTopicMessages 
} from './topicReader';
import * as path from 'path';

/**
 * Test the topic reader functions
 */
async function testTopicReader() {
  console.log('🧪 Testing Topic Reader Functions');
  console.log('=================================\n');
  
  const filePath = path.join(__dirname, '../../OhmniClean_log.ion');
  
  try {
    // Test 1: Get readable topics
    console.log('📋 Test 1: Get Readable Topics');
    console.log('------------------------------');
    const topics = await getReadableTopics(filePath);
    console.log(`Found ${topics.length} readable topics:`);
    
    // Show first 5 topics
    topics.slice(0, 5).forEach((topic, index) => {
      console.log(`${index + 1}. ${topic.name}`);
      console.log(`   Type: ${topic.type}`);
      console.log(`   Messages: ${topic.messageCount}`);
      console.log(`   Frequency: ${topic.frequency.toFixed(2)} Hz`);
      console.log(`   Time range: ${topic.firstTimestamp} - ${topic.lastTimestamp}`);
      console.log();
    });
    
    if (topics.length > 5) {
      console.log(`... and ${topics.length - 5} more topics\n`);
    }
    
    // Test 2: Get playback time range
    console.log('📋 Test 2: Get Playback Time Range');
    console.log('----------------------------------');
    const timeRange = await getPlaybackTimeRange(filePath);
    console.log(`Start time: ${timeRange.start}`);
    console.log(`End time: ${timeRange.end}`);
    console.log(`Duration: ${(timeRange.duration / 1000).toFixed(2)} seconds`);
    console.log();
    
    // Test 3: Get message at timestamp
    if (topics.length > 0) {
      console.log('📋 Test 3: Get Message at Timestamp');
      console.log('------------------------------------');
      const testTopic = topics[0]; // Use first topic
      const midTimestamp = (testTopic.firstTimestamp + testTopic.lastTimestamp) / 2;
      
      console.log(`Testing topic: ${testTopic.name}`);
      console.log(`Looking for message near timestamp: ${midTimestamp}`);
      
      const message = await getMessageAtTimestamp(filePath, testTopic.name, midTimestamp);
      if (message) {
        console.log(`✅ Found message:`);
        console.log(`   Timestamp: ${message.timestamp}`);
        console.log(`   Data type: ${typeof message.data}`);
        console.log(`   Data preview: ${JSON.stringify(message.data).substring(0, 200)}...`);
        if (message.sequenceNumber) {
          console.log(`   Sequence: ${message.sequenceNumber}`);
        }
      } else {
        console.log(`❌ No message found`);
      }
      console.log();
      
      // Test 4: Get topic messages (limited)
      console.log('📋 Test 4: Get Topic Messages (Limited)');
      console.log('---------------------------------------');
      const messages = await getTopicMessages(filePath, testTopic.name, undefined, undefined);
      console.log(`Total messages for ${testTopic.name}: ${messages.length}`);
      
      if (messages.length > 0) {
        console.log('First 3 messages:');
        messages.slice(0, 3).forEach((msg, index) => {
          console.log(`${index + 1}. Timestamp: ${msg.timestamp}`);
          console.log(`   Data: ${JSON.stringify(msg.data).substring(0, 100)}...`);
        });
      }
      console.log();
    }
    
    // Test 5: Test with a high-frequency topic
    console.log('📋 Test 5: Test High-Frequency Topic');
    console.log('-------------------------------------');
    const highFreqTopic = topics.find(t => t.frequency > 50);
    if (highFreqTopic) {
      console.log(`Testing high-frequency topic: ${highFreqTopic.name} (${highFreqTopic.frequency.toFixed(2)} Hz)`);
      
      // Get messages in a small time window
      const windowStart = highFreqTopic.firstTimestamp;
      const windowEnd = windowStart + 1000; // 1 second window
      
      const windowMessages = await getTopicMessages(filePath, highFreqTopic.name, windowStart, windowEnd);
      console.log(`Messages in 1-second window: ${windowMessages.length}`);
      console.log(`Effective frequency: ${windowMessages.length} Hz`);
      
      if (windowMessages.length > 0) {
        console.log('Sample message:');
        console.log(`  Timestamp: ${windowMessages[0].timestamp}`);
        console.log(`  Data: ${JSON.stringify(windowMessages[0].data).substring(0, 150)}...`);
      }
    } else {
      console.log('No high-frequency topics found');
    }
    
    console.log('\n✅ All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testTopicReader().catch(console.error);
}

export { testTopicReader };
