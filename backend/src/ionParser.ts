import * as ion from 'ion-js';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Interface for session information extracted from ION data
 */
export interface SessionInfo {
  sessionId?: string;
  timestamp?: string;
  duration?: number;
  startTime?: string;
  endTime?: string;
  version?: string;
  sessionCode?: string;
  operatorId?: string;
  operatorName?: string;
  mapId?: string;
  mapName?: string;
  result?: string;
  sessionType?: string;
  [key: string]: any; // Allow additional session properties
}

/**
 * Interface for robot metadata extracted from ION data
 */
export interface RobotInfo {
  robotId?: string;
  robotName?: string;
  model?: string;
  firmware?: string;
  serialNumber?: string;
  capabilities?: string[];
  sensors?: string[];
  enterpriseName?: string;
  enterpriseID?: string;
  siteID?: any;
  mapID?: string;
  mapVersionID?: string;
  releaseTrack?: string;
  apkVersion?: string;
  server?: string;
  autonomyServer?: string;
  configuration?: any; // Bot configuration object
  [key: string]: any; // Allow additional robot properties
}

/**
 * Interface for the complete metadata structure
 */
export interface IonMetadata {
  session: SessionInfo;
  robot: RobotInfo;
  rawData?: any; // Store raw parsed data for debugging
}

/**
 * Recursively converts ION values to JavaScript objects
 * Handles all ION value types: int, string, symbol, list, struct, blob, etc.
 */
function ionValueToJs(value: ion.dom.Value): any {
  if (value === null || value === undefined) {
    return null;
  }

  // Handle different ION types
  switch (value.getType()) {
    case ion.IonTypes.NULL:
      return null;

    case ion.IonTypes.BOOL:
      return value.booleanValue();

    case ion.IonTypes.INT:
      return value.numberValue();

    case ion.IonTypes.FLOAT:
      return value.numberValue();

    case ion.IonTypes.DECIMAL:
      return value.numberValue();

    case ion.IonTypes.TIMESTAMP:
      return value.timestampValue()?.toString();

    case ion.IonTypes.STRING:
      return value.stringValue();

    case ion.IonTypes.SYMBOL:
      return value.stringValue();

    case ion.IonTypes.BLOB:
      // Convert blob to base64 string for JSON serialization
      const blobBytes = value.uInt8ArrayValue();
      return blobBytes ? Buffer.from(blobBytes).toString('base64') : null;

    case ion.IonTypes.CLOB:
      // Convert clob to string
      const clobBytes = value.uInt8ArrayValue();
      return clobBytes ? Buffer.from(clobBytes).toString('utf8') : null;

    case ion.IonTypes.LIST:
      // Recursively process list elements
      const listElements: any[] = [];
      for (let i = 0; i < value.elements().length; i++) {
        listElements.push(ionValueToJs(value.elements()[i]));
      }
      return listElements;

    case ion.IonTypes.SEXP:
      // S-expressions are treated like lists
      const sexpElements: any[] = [];
      for (let i = 0; i < value.elements().length; i++) {
        sexpElements.push(ionValueToJs(value.elements()[i]));
      }
      return sexpElements;

    case ion.IonTypes.STRUCT:
      // Recursively process struct fields
      const structObj: any = {};
      const fields = value.fields();
      for (const field of fields) {
        const fieldName = field[0]; // field is a tuple [fieldName, value]
        const fieldValue = ionValueToJs(field[1]);
        if (fieldName) {
          structObj[fieldName] = fieldValue;
        }
      }
      return structObj;

    default:
      // Fallback for unknown types
      console.warn(`Unknown ION type: ${value.getType()}`);
      return value.toString();
  }
}

/**
 * Extracts session information from parsed ION data
 */
function extractSessionInfo(data: any): SessionInfo {
  const session: SessionInfo = {};

  // Look for session data in common locations
  if (data.session) {
    Object.assign(session, data.session);
  }

  if (data.metadata?.session) {
    Object.assign(session, data.metadata.session);
  }

  // OhmniClean specific: sessionInfo is under metadata.sessionInfo
  if (data.metadata?.sessionInfo) {
    Object.assign(session, data.metadata.sessionInfo);
  }

  // Extract common session fields from root level
  if (data.sessionId) session.sessionId = data.sessionId;
  if (data.timestamp) session.timestamp = data.timestamp;
  if (data.startTime) session.startTime = data.startTime;
  if (data.endTime) session.endTime = data.endTime;
  if (data.duration) session.duration = data.duration;
  if (data.version) session.version = data.version;

  // Map OhmniClean specific fields to standard names
  if (data.metadata?.sessionInfo) {
    const sessionInfo = data.metadata.sessionInfo;
    if (sessionInfo.session_id) session.sessionId = sessionInfo.session_id;
    if (sessionInfo.start_time) session.startTime = sessionInfo.start_time;
    if (sessionInfo.end_time) session.endTime = sessionInfo.end_time;
    if (sessionInfo.duration) session.duration = sessionInfo.duration;
    if (sessionInfo.sessionCode) session.sessionCode = sessionInfo.sessionCode;
    if (sessionInfo.operator_id) session.operatorId = sessionInfo.operator_id;
    if (sessionInfo.operator_name) session.operatorName = sessionInfo.operator_name;
    if (sessionInfo.map_id) session.mapId = sessionInfo.map_id;
    if (sessionInfo.map_name) session.mapName = sessionInfo.map_name;
    if (sessionInfo.result) session.result = sessionInfo.result;
    if (sessionInfo.sessionType) session.sessionType = sessionInfo.sessionType;
  }

  return session;
}

/**
 * Extracts robot information from parsed ION data
 */
function extractRobotInfo(data: any): RobotInfo {
  const robot: RobotInfo = {};

  // Look for robot data in common locations
  if (data.robot) {
    Object.assign(robot, data.robot);
  }

  if (data.metadata?.robot) {
    Object.assign(robot, data.metadata.robot);
  }

  // OhmniClean specific: botInfo is under metadata.botInfo
  if (data.metadata?.botInfo) {
    Object.assign(robot, data.metadata.botInfo);
  }

  // Extract common robot fields from root level
  if (data.robotId) robot.robotId = data.robotId;
  if (data.robotName) robot.robotName = data.robotName;
  if (data.model) robot.model = data.model;
  if (data.firmware) robot.firmware = data.firmware;
  if (data.serialNumber) robot.serialNumber = data.serialNumber;
  if (data.capabilities) robot.capabilities = data.capabilities;
  if (data.sensors) robot.sensors = data.sensors;

  // Map OhmniClean specific fields to standard names
  if (data.metadata?.botInfo) {
    const botInfo = data.metadata.botInfo;
    if (botInfo.botID) robot.robotId = botInfo.botID;
    if (botInfo.botName) robot.robotName = botInfo.botName;
    if (botInfo.enterpriseName) robot.enterpriseName = botInfo.enterpriseName;
    if (botInfo.enterpriseID) robot.enterpriseID = botInfo.enterpriseID;
    if (botInfo.siteID) robot.siteID = botInfo.siteID;
    if (botInfo.mapID) robot.mapID = botInfo.mapID;
    if (botInfo.mapVersionID) robot.mapVersionID = botInfo.mapVersionID;
    if (botInfo.releaseTrack) robot.releaseTrack = botInfo.releaseTrack;
    if (botInfo.amrVersion) robot.firmware = botInfo.amrVersion;
    if (botInfo.apkVersion) robot.apkVersion = botInfo.apkVersion;
    if (botInfo.server) robot.server = botInfo.server;
    if (botInfo.autonomyServer) robot.autonomyServer = botInfo.autonomyServer;
  }

  // Extract bot configuration if available
  if (data.metadata?.botConfig) {
    robot.configuration = data.metadata.botConfig;
  }

  return robot;
}

/**
 * Main function to parse ION file and extract metadata
 * @param filePath - Path to the binary ION file
 * @returns Promise<IonMetadata> - Structured metadata containing session and robot info
 */
export async function parseIonMetadata(filePath: string): Promise<IonMetadata> {
  try {
    // Validate file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`ION file not found: ${filePath}`);
    }

    // Read the binary ION file
    const ionData = fs.readFileSync(filePath);

    // Parse the ION data using DOM loader
    const domValues = ion.dom.loadAll(ionData);
    const parsedValues: any[] = [];

    // Convert all DOM values to JavaScript objects
    for (const domValue of domValues) {
      parsedValues.push(ionValueToJs(domValue));
    }

    // If we have multiple values, wrap them in an array
    // If single value, use it directly
    const rootData = parsedValues.length === 1 ? parsedValues[0] : parsedValues;

    // Extract session and robot metadata
    const sessionInfo = extractSessionInfo(rootData);
    const robotInfo = extractRobotInfo(rootData);

    return {
      session: sessionInfo,
      robot: robotInfo,
      rawData: rootData // Include raw data for debugging
    };

  } catch (error) {
    throw new Error(`Failed to parse ION file: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Utility function to validate ION file format
 */
export function isValidIonFile(filePath: string): boolean {
  try {
    if (!fs.existsSync(filePath)) {
      return false;
    }

    const ionData = fs.readFileSync(filePath);

    // Try to load at least one value using DOM loader
    const domValues = ion.dom.loadAll(ionData);
    return domValues.length > 0;
  } catch {
    return false;
  }
}
